{"name": "vite-vue-js-starter-template", "description": "Vue3 JavaScript Starter Project For Vite.", "version": "0.0.0", "type": "module", "license": "MIT", "author": {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>", "url": "https://doc.starimmortal.com"}, "homepage": "https://elanyoung.github.io/vite-vue-js-starter-template", "repository": {"type": "git", "url": "**************:ElanYoung/vite-vue-js-starter-template.git"}, "bugs": {"url": "https://github.com/ElanYoung/vite-vue-js-starter-template/issues"}, "scripts": {"dev": "vite", "build": "vite build", "preview": "vite preview", "stylelint": "stylelint \"**/*.{html,vue,css,scss}\" --cache --cache-location node_modules/.cache/stylelint/", "stylelint:fix": "stylelint \"**/*.{html,vue,css,scss}\" --cache --cache-location node_modules/.cache/stylelint/ --fix", "prepare": "husky"}, "dependencies": {"@tailwindcss/vite": "^4.1.11", "@vueuse/core": "^13.3.0", "axios": "^1.9.0", "crypto-js": "^4.2.0", "dayjs": "^1.11.13", "image-conversion": "^2.1.1", "jsencrypt": "^3.3.2", "lucide-vue-next": "^0.525.0", "mitt": "^3.0.1", "pinia": "^3.0.2", "pinia-plugin-persistedstate": "^4.3.0", "tailwindcss": "^4.1.11", "tdesign-vue-next": "^1.13.0", "vue": "^3.5.15", "vue-router": "^4.5.1"}, "devDependencies": {"@commitlint/cli": "^19.8.1", "@commitlint/config-conventional": "^19.8.1", "@iconify-json/material-symbols": "^1.2.22", "@iconify-json/tdesign": "^1.2.8", "@vitejs/plugin-vue": "^5.2.4", "autoprefixer": "^10.4.21", "cz-git": "^1.11.1", "husky": "^9.1.7", "less": "^4.3.0", "lint-staged": "^15.5.2", "postcss-html": "^1.8.0", "postcss-less": "^6.0.0", "prettier": "^3.5.3", "stylelint": "^16.19.1", "stylelint-config-recess-order": "^6.0.0", "stylelint-config-recommended-vue": "^1.6.0", "stylelint-config-standard-less": "^3.0.1", "stylelint-prettier": "^5.0.3", "unplugin-auto-import": "^19.3.0", "unplugin-icons": "^22.1.0", "unplugin-vue-components": "^28.7.0", "vite": "^6.3.5"}, "husky": {"hooks": {"pre-commit": "lint-staged", "commit-msg": "commitlint -E HUSKY_GIT_PARAMS"}}, "lint-staged": {"*.{js,jsx,ts,tsx}": ["prettier --write"], "*.vue": ["prettier --write", "stylelint --fix"], "*.{html,sass,scss,less}": ["prettier --write", "stylelint --fix"], "package.json": ["prettier --write"], "*.md": ["prettier --write"]}, "config": {"commitizen": {"path": "node_modules/cz-git"}}, "engines": {"node": "^18.0.0 || >=20.0.0 || >=22.0.0", "pnpm": ">=9"}}