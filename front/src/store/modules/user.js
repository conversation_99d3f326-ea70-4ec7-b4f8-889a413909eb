import { defineStore } from 'pinia';
import { ref } from 'vue';
import { getInfo, isLogin, login, logout } from '@/api/login';
import defAva from '@/assets/images/profile.jpg';

export const useUserStore = defineStore('user', () => {
  // 状态
  const tenantId = ref(undefined);
  const userId = ref(undefined);
  const token = ref(undefined);
  const name = ref('');
  const avatar = ref('');
  const roles = ref([]);
  const permissions = ref([]);

  // 检查登录状态
  const isLoginStatus = () => {
    return new Promise((resolve, reject) => {
      if (token.value) {
        isLogin()
          .then((res) => resolve(res.data))
          .catch((error) => {
            reject(error);
          });
      } else {
        resolve(false);
      }
    });
  };

  // 登录方法
  const loginUser = (userInfo) => {
    const username = userInfo.username.trim();
    const { password } = userInfo;
    const { code } = userInfo;
    const { uuid } = userInfo;
    const clientId = "e5cd7e4891bf95d1d19206ce24a7b32e"
    const grantType = "password"

    return new Promise((resolve, reject) => {
      login({ username, password, code, uuid, clientId, grantType })
        .then((res) => {
          token.value = res.data.access_token;
          resolve();
        })
        .catch((error) => {
          reject(error);
        });
    });
  };

  // 更新头像
  const updateAvatar = (avatarUrl) => {
    avatar.value = !avatarUrl ? defAva : avatarUrl;
  };

  // 获取用户信息
  const getUserInfo = () => {
    return new Promise((resolve, reject) => {
      getInfo()
        .then((res) => {
          const { user } = res.data;
          const avatarUrl = !user.avatar ? defAva : user.avatar;

          if (res.data.roles && res.data.roles.length > 0) {
            // 验证返回的roles是否是一个非空数组
            roles.value = res.data.roles;
            permissions.value = res.data.permissions;
          } else {
            roles.value = ['ROLE_DEFAULT'];
          }

          tenantId.value = user.tenantId;
          userId.value = user.userId;
          name.value = user.userName;
          avatar.value = avatarUrl;
          resolve(res);
        })
        .catch((error) => {
          reject(error);
        });
    });
  };

  // 登出方法
  const logoutUser = () => {
    return new Promise((resolve, reject) => {
      logout()
        .then(() => {
          token.value = undefined;
          tenantId.value = undefined;
          userId.value = undefined;
          name.value = '';
          avatar.value = '';
          roles.value = [];
          permissions.value = [];
          resolve();
        })
        .catch((error) => {
          reject(error);
        });
    });
  };



  // 获取用户显示名称
  const getDisplayName = () => {
    return name.value || '未登录';
  };

  // 检查用户权限
  const hasRole = (role) => {
    return roles.value.includes(role);
  };

  // 检查用户权限
  const hasPermission = (permission) => {
    return permissions.value.includes(permission);
  };

  return {
    // 状态
    tenantId,
    userId,
    token,
    name,
    avatar,
    roles,
    permissions,

    // 方法
    isLogin: isLoginStatus,
    login: loginUser,
    updateAvatar,
    getUserInfo,
    logout: logoutUser,
    getDisplayName,
    hasRole,
    hasPermission
  };
}, {
  persist: {
    storage: localStorage,
    key: 'Admin-Token',
    pick: ['token'],
  },
});
