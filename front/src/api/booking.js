import { get, post, put, _delete } from '@/utils/request.js'

/**
 * 获取预约列表
 * @param {Object} query 查询参数
 * @param {number} query.pageNum 页码
 * @param {number} query.pageSize 每页数量
 * @param {string} query.status 预约状态
 * @returns {Promise}
 */
export function ListBookings(query) {
  return get('/houseKeeping/booking/list', query)
}

/**
 * 获取预约详情
 * @param {string|number} id 预约ID
 * @returns {Promise}
 */
export function GetBookingDetail(id) {
  return get(`/houseKeeping/booking/detail/${id}`)
}

/**
 * 创建预约
 * @param {Object} data 预约数据
 * @param {string} data.serviceId 服务ID
 * @param {string} data.contactName 联系人姓名
 * @param {string} data.contactPhone 联系电话
 * @param {string} data.serviceAddress 服务地址
 * @param {string} data.serviceDate 服务日期
 * @param {string} data.serviceTime 服务时间段
 * @param {string} data.specialRequirements 特殊要求
 * @returns {Promise}
 */
export function CreateBooking(data) {
  return post('/houseKeeping/booking/create', data)
}

/**
 * 更新预约
 * @param {Object} data 预约数据
 * @param {string|number} data.id 预约ID
 * @returns {Promise}
 */
export function UpdateBooking(data) {
  return put('/houseKeeping/booking/update', data)
}

/**
 * 取消预约
 * @param {string|number} id 预约ID
 * @param {string} reason 取消原因
 * @returns {Promise}
 */
export function CancelBooking(id, reason) {
  return post(`/houseKeeping/booking/cancel/${id}`, { reason })
}

/**
 * 删除预约
 * @param {string|number} id 预约ID
 * @returns {Promise}
 */
export function DeleteBooking(id) {
  return _delete(`/houseKeeping/booking/delete/${id}`)
}

/**
 * 评价服务
 * @param {Object} data 评价数据
 * @param {string|number} data.bookingId 预约ID
 * @param {number} data.rating 评分 (1-5)
 * @param {string} data.comment 评价内容
 * @param {Array} data.images 评价图片
 * @returns {Promise}
 */
export function RateService(data) {
  return post('/houseKeeping/booking/rate', data)
}

/**
 * 获取预约统计信息
 * @returns {Promise}
 */
export function GetBookingStats() {
  return get('/houseKeeping/booking/stats')
}
