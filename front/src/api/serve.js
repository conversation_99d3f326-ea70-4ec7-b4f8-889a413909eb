import {get} from '@/utils/request.js'

/**
 * 获取服务列表
 * @param query
 * @returns {Promise<axios.AxiosResponse<any>>}
 * @constructor
 */
export function ListServe(query) {
  return get('/houseKeeping/serve/list', query)
}

/**
 * 获取服务详情
 * @param serveId
 * @returns {Promise<axios.AxiosResponse<any>>}
 * @constructor
 */
export function GetServeDetail(serveId){
  return get(`/houseKeeping/serve/${serveId}`)
}
