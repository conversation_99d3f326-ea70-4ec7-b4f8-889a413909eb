import { get, post, _delete } from '@/utils/request.js'

/**
 * 查询OSS对象基于id串
 * @param {string|Array} ossId
 * @returns {Promise}
 */
export function listByIds(ossId) {
  const ids = Array.isArray(ossId) ? ossId.join(',') : ossId;
  return get(`/resource/oss/listByIds/${ids}`);
}

/**
 * 查询OSS对象基于url串
 * @param {string} urls
 * @returns {Promise}
 */
export function listByUrls(urls) {
  return get('/resource/oss/listByUrls', { 
    urls: encodeURIComponent(urls) 
  });
}

/**
 * 查询OSS对象存储详细
 * @param {number} ossId
 * @returns {Promise}
 */
export function getOss(ossId) {
  return get(`/resource/oss/${ossId}`);
}

/**
 * 删除OSS对象存储
 * @param {number|Array} ossId
 * @returns {Promise}
 */
export function delOss(ossId) {
  const ids = Array.isArray(ossId) ? ossId.join(',') : ossId;
  return _delete(`/resource/oss/${ids}`);
}

/**
 * 上传文件
 * @param {FormData} formData
 * @returns {Promise}
 */
export function uploader(formData) {
  return post('/resource/oss/upload', formData, {
    headers: {
      'Content-Type': 'multipart/form-data',
    },
  });
}
