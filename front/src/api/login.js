import { request } from '@/utils/request';

// pc端固定客户端授权id
const clientId = import.meta.env.VITE_CLIENT_ID;

/**
 * 是否登录状态
 * @returns {Promise}
 */
export function isLogin() {
  return request.get({
    url: '/auth/isLogin',
  });
}

/**
 * 登录方法
 * @param {Object} loginData 登录参数
 * @param {string} loginData.username 用户名
 * @param {string} loginData.password 密码
 * @param {string} loginData.code 验证码
 * @param {string} loginData.uuid 验证码UUID
 * @param {string} [loginData.clientId] 客户端ID
 * @param {string} [loginData.grantType] 授权类型
 * @returns {Promise}
 */
export function login(loginData) {
  const data = {
    ...loginData,
    clientId: loginData.clientId || clientId,
    grantType: loginData.grantType || 'password',
  };
  return request.post(
    {
      url: '/auth/login',
      data,
    },
    {
      withToken: false,
      withEncrypt: true,
    },
  );
}

/**
 * 注册方法
 * @param {Object} data 注册参数
 * @returns {Promise}
 */
export function register(data) {
  const params = {
    ...data,
    tenantId: "000000",
    clientId: "e5cd7e4891bf95d1d19206ce24a7b32e",
    userType: "sys_user",
    grantType: 'RegisterBody',
  };
  return request.post(
    {
      url: '/auth/register',
      data: params,
    },
    {
      withToken: false,
      withEncrypt: true,
    },
  );
}

/**
 * 第三方登录
 * @param {Object} data 登录参数
 * @returns {Promise}
 */
export function callback(data) {
  const LoginData = {
    ...data,
    clientId,
    grantType: 'social',
  };
  return request.post({
    url: '/auth/social/callback',
    data: LoginData,
  });
}

/**
 * 获取用户详细信息
 * @returns {Promise}
 */
export function getInfo() {
  return request.get({
    url: '/system/user/getInfo',
  });
}

/**
 * 退出方法
 * @returns {Promise}
 */
export function logout() {
  return request.post({
    url: '/auth/logout',
  });
}

/**
 * 获取验证码
 * @returns {Promise}
 */
export function getCodeImg() {
  return request.get(
    {
      url: '/auth/code',
      timeout: 20000,
    },
    {
      withToken: false,
    },
  );
}

/**
 * 短信验证码
 * @returns {Promise}
 */
export function getCodeSms() {
  return request.get(
    {
      url: '/resource/sms/code',
      timeout: 20000,
    },
    {
      withToken: false,
    },
  );
}

/**
 * 获取租户列表
 * @param {boolean} withToken 是否携带token
 * @returns {Promise}
 */
export function getTenantList(withToken) {
  return request.get(
    {
      url: '/auth/tenant/list',
    },
    {
      withToken,
    },
  );
}
