<script setup>
import { computed } from 'vue'
import { useRoute } from 'vue-router'
import Header from '@/components/Header/index.vue'
import Footer from '@/components/Footer/index.vue'

const route = useRoute()

// 不需要显示Header和Footer的页面
const hideLayoutPages = ['login']

// 计算是否显示布局组件
const showLayout = computed(() => {
  return !hideLayoutPages.includes(route.name)
})
</script>

<template>
  <div class="app">
    <!-- Header -->
    <Header v-if="showLayout" />
    <!-- Main Content -->
    <main class="main-content" :class="{ 'full-page': !showLayout }">
      <RouterView />
    </main>
    <!-- Footer -->
    <Footer v-if="showLayout" />
  </div>
</template>

<style lang="less">
.app {
  min-height: 100vh;
  display: flex;
  flex-direction: column;
  
  .main-content {
    flex: 1;
    
    &.full-page {
      min-height: 100vh;
    }
  }
}
</style>


