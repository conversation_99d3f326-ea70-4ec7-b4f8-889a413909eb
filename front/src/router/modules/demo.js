export default [
  {
    path: '/',
    name: 'index',
    component: () => import('@/views/index/index.vue'),
  },
  {
    path: '/login',
    name: 'login',
    component: () => import('@/views/login/index.vue'),
  },
  {
    path: '/services',
    name: 'services',
    component: () => import('@/views/services/index.vue'),
  },
  {
    path: '/services/original',
    name: 'services-original',
    component: () => import('@/views/services/index.vue'),
  },
  {
    path: '/service/:id',
    name: 'service-detail',
    component: () => import('@/views/service/detail.vue'),
  },
  {
    path: '/test-related-services',
    name: 'test-related-services',
    component: () => import('@/views/service/TestRelatedServices.vue'),
  },
  {
    path: '/my-bookings',
    name: 'my-bookings',
    component: () => import('@/views/my-bookings/index.vue'),
  },
  {
    path: '/join-us',
    name: 'join-us',
    component: () => import('@/views/join-us/index.vue'),
  },
];
