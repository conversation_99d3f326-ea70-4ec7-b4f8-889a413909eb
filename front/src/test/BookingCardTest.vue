<template>
  <div class="test-container">
    <h2>预约卡片取消原因测试</h2>

    <!-- 测试已取消且有取消原因的预约 -->
    <div class="test-section">
      <h3>已取消预约（有取消原因）</h3>
      <BookingCard :booking="cancelledBookingWithReason" />
    </div>

    <!-- 测试已取消但无取消原因的预约 -->
    <div class="test-section">
      <h3>已取消预约（无取消原因）</h3>
      <BookingCard :booking="cancelledBookingWithoutReason" />
    </div>

    <!-- 测试长取消原因的预约 -->
    <div class="test-section">
      <h3>已取消预约（长取消原因）</h3>
      <BookingCard :booking="cancelledBookingWithLongReason" />
    </div>

    <!-- 测试正常状态的预约 -->
    <div class="test-section">
      <h3>正常状态预约</h3>
      <BookingCard :booking="normalBooking" />
    </div>
  </div>
</template>

<script setup>
import { ref } from 'vue'
import BookingCard from '@/views/my-bookings/BookingCard.vue'

// 测试数据：已取消且有取消原因的预约
const cancelledBookingWithReason = ref({
  reservationId: "1949813613735763969",
  serveId: "1946607589214228482",
  serve: {
    serveId: "1946607589214228482",
    name: "深度清洁",
    category: "清洁服务",
    image: "https://minioapi.dabuguoni.top/housekeeping/2025/07/28/329bc37d1a2b421cb36263aa1e8f512b.png",
    description: "针对房屋死角、厨房油污、卫生间水垢等进行彻底清洁，还原家居本来面貌。",
    price: "0.01",
    unit: "元",
    serviceUnit: "次",
    tag: "[\"全屋深度清洁\",\"家电清洗\",\"除菌除螨\"]",
    star: 5
  },
  serviceDate: "2025-07-29",
  contactName: "张三",
  serviceTime: "19:00-21:00",
  phone: "15555555555",
  address: "广州市西华路34号",
  special: "家里有宠物，请注意安全",
  status: "已取消",
  staffId: null,
  staff: null,
  customerId: "1949345404117106689",
  createTime: "2025-07-28 20:46:06",
  cancelReason: "临时有急事需要出差，无法在预约时间接受服务，深感抱歉。",
  updateTime: "2025-07-28 22:56:01",
  serviceTags: ["全屋深度清洁", "家电清洗", "除菌除螨"]
})

// 测试数据：已取消但无取消原因的预约
const cancelledBookingWithoutReason = ref({
  reservationId: "1949813613735763970",
  serveId: "1946607589214228482",
  serve: {
    serveId: "1946607589214228482",
    name: "深度清洁",
    category: "清洁服务",
    image: "https://minioapi.dabuguoni.top/housekeeping/2025/07/28/329bc37d1a2b421cb36263aa1e8f512b.png",
    description: "针对房屋死角、厨房油污、卫生间水垢等进行彻底清洁，还原家居本来面貌。",
    price: "0.01",
    unit: "元",
    serviceUnit: "次",
    tag: "[\"全屋深度清洁\",\"家电清洗\",\"除菌除螨\"]",
    star: 5
  },
  serviceDate: "2025-07-29",
  contactName: "李四",
  serviceTime: "19:00-21:00",
  phone: "15555555556",
  address: "广州市天河路56号",
  special: null,
  status: "已取消",
  staffId: null,
  staff: null,
  customerId: "1949345404117106689",
  createTime: "2025-07-28 20:46:06",
  cancelReason: null,
  updateTime: "2025-07-28 22:56:01",
  serviceTags: ["全屋深度清洁", "家电清洗", "除菌除螨"]
})

// 测试数据：已取消且有长取消原因的预约
const cancelledBookingWithLongReason = ref({
  reservationId: "1949813613735763971",
  serveId: "1946607589214228482",
  serve: {
    serveId: "1946607589214228482",
    name: "深度清洁",
    category: "清洁服务",
    image: "https://minioapi.dabuguoni.top/housekeeping/2025/07/28/329bc37d1a2b421cb36263aa1e8f512b.png",
    description: "针对房屋死角、厨房油污、卫生间水垢等进行彻底清洁，还原家居本来面貌。",
    price: "0.01",
    unit: "元",
    serviceUnit: "次",
    tag: "[\"全屋深度清洁\",\"家电清洗\",\"除菌除螨\"]",
    star: 5
  },
  serviceDate: "2025-07-29",
  contactName: "赵六",
  serviceTime: "14:00-16:00",
  phone: "15555555558",
  address: "广州市海珠区新港东路123号",
  special: "需要特别注意保护地板",
  status: "已取消",
  staffId: null,
  staff: null,
  customerId: "1949345404117106689",
  createTime: "2025-07-28 20:46:06",
  cancelReason: "非常抱歉需要取消此次预约。由于家中突发紧急情况，老人身体不适需要立即送医院治疗，预计需要在医院陪护一周时间。同时，家中还有小孩需要照顾，实在无法安排时间接受清洁服务。等家中情况稳定后，我会重新预约服务。再次为此次取消带来的不便深表歉意。",
  updateTime: "2025-07-28 22:56:01",
  serviceTags: ["全屋深度清洁", "家电清洗", "除菌除螨"]
})

// 测试数据：正常状态的预约
const normalBooking = ref({
  reservationId: "1949514666408538113",
  serveId: "1946605621091602433",
  serve: {
    serveId: "1946605621091602433",
    name: "日常保洁",
    category: "清洁服务",
    image: "https://minioapi.dabuguoni.top/housekeeping/2025/07/28/dc2f54217b064aa4a965ef3889f8a1ef.png",
    description: "包括地面清洁、家具擦拭、厨房清洁、卫生间清洁等基础保洁服务，让您的家保持整洁。",
    price: "0.01",
    unit: "元",
    serviceUnit: "次",
    tag: "[\"日常保洁\",\"深度清洁\",\"开荒保洁\"]",
    star: 5
  },
  serviceDate: "2025-07-28",
  contactName: "王五",
  serviceTime: "08:00-10:00",
  phone: "15555555557",
  address: "广州市天河路34号",
  special: "家里有宠物",
  status: "待支付",
  staffId: "1949291203198509058",
  staff: {
    staffId: "1949291203198509058",
    name: "张阿姨",
    title: "高级保洁师",
    gender: 1,
    phone: "17777777777",
    age: 28,
    star: 5,
    image: "https://minioapi.dabuguoni.top/housekeeping/2025/07/28/526c6e99de8145f9b333f5bc4926b4a9.png",
    status: "空闲中",
    tag: "[\"家庭保洁\",\"深度清洁\",\"收纳整洁\"]",
    experience: "十年保洁经验"
  },
  customerId: "1949345404117106689",
  createTime: "2025-07-28 00:58:11",
  cancelReason: null,
  updateTime: "2025-07-28 02:07:51",
  serviceTags: ["日常保洁", "深度清洁", "开荒保洁"]
})
</script>

<style lang="less" scoped>
.test-container {
  padding: 2rem;
  max-width: 800px;
  margin: 0 auto;
}

.test-section {
  margin-bottom: 3rem;

  h3 {
    margin-bottom: 1rem;
    color: #1f2937;
    font-size: 1.125rem;
    font-weight: 600;
  }
}

h2 {
  text-align: center;
  margin-bottom: 2rem;
  color: #1f2937;
  font-size: 1.5rem;
  font-weight: 700;
}
</style>
