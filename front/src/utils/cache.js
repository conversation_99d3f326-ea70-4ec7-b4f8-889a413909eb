/**
 * 缓存工具类
 * 提供 localStorage 和 sessionStorage 的封装
 */

/**
 * localStorage 操作
 */
const local = {
  /**
   * 设置 localStorage
   * @param {string} key 键名
   * @param {any} value 值
   */
  set(key, value) {
    try {
      localStorage.setItem(key, JSON.stringify(value))
    } catch (error) {
      console.error('localStorage set error:', error)
    }
  },

  /**
   * 获取 localStorage
   * @param {string} key 键名
   * @returns {any} 值
   */
  get(key) {
    try {
      const value = localStorage.getItem(key)
      return value ? JSON.parse(value) : null
    } catch (error) {
      console.error('localStorage get error:', error)
      return null
    }
  },

  /**
   * 设置 localStorage (JSON格式)
   * @param {string} key 键名
   * @param {any} value 值
   */
  setJSON(key, value) {
    this.set(key, value)
  },

  /**
   * 获取 localStorage (JSON格式)
   * @param {string} key 键名
   * @returns {any} 值
   */
  getJSON(key) {
    return this.get(key)
  },

  /**
   * 删除 localStorage
   * @param {string} key 键名
   */
  remove(key) {
    try {
      localStorage.removeItem(key)
    } catch (error) {
      console.error('localStorage remove error:', error)
    }
  },

  /**
   * 清空 localStorage
   */
  clear() {
    try {
      localStorage.clear()
    } catch (error) {
      console.error('localStorage clear error:', error)
    }
  }
}

/**
 * sessionStorage 操作
 */
const session = {
  /**
   * 设置 sessionStorage
   * @param {string} key 键名
   * @param {any} value 值
   */
  set(key, value) {
    try {
      sessionStorage.setItem(key, JSON.stringify(value))
    } catch (error) {
      console.error('sessionStorage set error:', error)
    }
  },

  /**
   * 获取 sessionStorage
   * @param {string} key 键名
   * @returns {any} 值
   */
  get(key) {
    try {
      const value = sessionStorage.getItem(key)
      return value ? JSON.parse(value) : null
    } catch (error) {
      console.error('sessionStorage get error:', error)
      return null
    }
  },

  /**
   * 设置 sessionStorage (JSON格式)
   * @param {string} key 键名
   * @param {any} value 值
   */
  setJSON(key, value) {
    this.set(key, value)
  },

  /**
   * 获取 sessionStorage (JSON格式)
   * @param {string} key 键名
   * @returns {any} 值
   */
  getJSON(key) {
    return this.get(key)
  },

  /**
   * 删除 sessionStorage
   * @param {string} key 键名
   */
  remove(key) {
    try {
      sessionStorage.removeItem(key)
    } catch (error) {
      console.error('sessionStorage remove error:', error)
    }
  },

  /**
   * 清空 sessionStorage
   */
  clear() {
    try {
      sessionStorage.clear()
    } catch (error) {
      console.error('sessionStorage clear error:', error)
    }
  }
}

/**
 * 缓存工具对象
 */
export const cache = {
  local,
  session
}

export default cache
