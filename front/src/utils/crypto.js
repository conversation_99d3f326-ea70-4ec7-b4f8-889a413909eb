import CryptoJS from 'crypto-js'
import JSEncrypt from 'jsencrypt'

// RSA公钥（用于加密AES密钥）
const RSA_PUBLIC_KEY = import.meta.env.VITE_APP_RSA_PUBLIC_KEY || 'MFwwDQYJKoZIhvcNAQEBBQADSwAwSAJBAKoR8mX0rGKLqzcWmOzbfj64K8ZIgOdHnzkXSOVOZbFu/TJhZ7rFAN+eaGkl3C4buccQd/EjEsj9ir7ijT7h96MCAwEAAQ=='

// RSA私钥（用于解密后端响应中的AES密钥）
const RSA_PRIVATE_KEY = import.meta.env.VITE_APP_RSA_PRIVATE_KEY || 'MIIBVAIBADANBgkqhkiG9w0BAQEFAASCAT4wggE6AgEAAkEAmc3CuPiGL/LcIIm7zryCEIbl1SPzBkr75E2VMtxegyZ1lYRD+7TZGAPkvIsBcaMs6Nsy0L78n2qh+lIZMpLH8wIDAQABAkEAk82Mhz0tlv6IVCyIcw/s3f0E+WLmtPFyR9/WtV3Y5aaejUkU60JpX4m5xNR2VaqOLTZAYjW8Wy0aXr3zYIhhQQIhAMfqR9oFdYw1J9SsNc+CrhugAvKTi0+BF6VoL6psWhvbAiEAxPPNTmrkmrXwdm/pQQu3UOQmc2vCZ5tiKpW10CgJi8kCIFGkL6utxw93Ncj4exE/gPLvKcT+1Emnoox+O9kRXss5AiAMtYLJDaLEzPrAWcZeeSgSIzbL+ecokmFKSDDcRske6QIgSMkHedwND1olF8vlKsJUGK3BcdtM8w4Xq7BpSBwsloE='

// 加密响应头名称
export const encryptHeader = 'encrypt-key'

// 内容语言头名称
export const contentLanguage = 'content-language'

/**
 * 随机生成32位字符串作为AES密钥
 * @param {number} length 密钥长度，默认32位
 * @returns {string} 随机字符串
 */
function generateRandomString(length = 32) {
  const characters = 'ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789'
  let result = ''
  for (let i = 0; i < length; i++) {
    result += characters.charAt(Math.floor(Math.random() * characters.length))
  }
  return result
}

/**
 * 生成AES密钥
 * @returns {CryptoJS.lib.WordArray} AES密钥
 */
export function generateAesKey() {
  return CryptoJS.enc.Utf8.parse(generateRandomString())
}

/**
 * Base64编码
 * @param {CryptoJS.lib.WordArray} key AES密钥
 * @returns {string} Base64编码后的字符串
 */
export function encryptBase64(key) {
  return CryptoJS.enc.Base64.stringify(key)
}

/**
 * Base64解码
 * @param {string} base64Str Base64字符串
 * @returns {CryptoJS.lib.WordArray} 解码后的密钥
 */
export function decryptBase64(base64Str) {
  return CryptoJS.enc.Base64.parse(base64Str)
}

/**
 * RSA加密
 * @param {string} data 要加密的数据
 * @returns {string} 加密后的数据
 */
export function encrypt(data) {
  const jsEncrypt = new JSEncrypt()
  jsEncrypt.setPublicKey(RSA_PUBLIC_KEY)
  return jsEncrypt.encrypt(data)
}

/**
 * RSA解密
 * @param {string} data 要解密的数据
 * @returns {string} 解密后的数据
 */
export function decrypt(data) {
  const jsEncrypt = new JSEncrypt()
  jsEncrypt.setPrivateKey(RSA_PRIVATE_KEY)
  return jsEncrypt.decrypt(data)
}

/**
 * AES加密
 * @param {string} data 要加密的数据
 * @param {CryptoJS.lib.WordArray} key AES密钥
 * @returns {string} 加密后的数据
 */
export function encryptWithAes(data, key) {
  const encrypted = CryptoJS.AES.encrypt(data, key, {
    mode: CryptoJS.mode.ECB,
    padding: CryptoJS.pad.Pkcs7
  })

  return encrypted.toString()
}

/**
 * AES解密
 * @param {string} encryptedData 加密的数据
 * @param {CryptoJS.lib.WordArray} key AES密钥
 * @returns {string} 解密后的数据
 */
export function decryptWithAes(encryptedData, key) {
  const decrypted = CryptoJS.AES.decrypt(encryptedData, key, {
    mode: CryptoJS.mode.ECB,
    padding: CryptoJS.pad.Pkcs7
  })

  return decrypted.toString(CryptoJS.enc.Utf8)
}
