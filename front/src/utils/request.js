import axios from 'axios';
import { MessagePlugin } from 'tdesign-vue-next';
import { useUserStore } from '@/store/modules/user';
import { cache } from '@/utils/cache';
import router from '@/router';
import {
  generateAesKey,
  encryptBase64,
  decryptBase64,
  encrypt,
  decrypt,
  encryptWithAes,
  decryptWithAes,
  encryptHeader,
  contentLanguage
} from '@/utils/crypto';

// 处理未授权错误（401）
const handleUnauthorized = async () => {
  try {
    const userStore = useUserStore();

    // 清除用户信息和token
    await userStore.logout();

    // 获取当前路由路径，用于登录后重定向
    const currentPath = router.currentRoute.value.fullPath;

    // 如果当前不在登录页，则重定向到登录页
    if (currentPath !== '/login') {
      await router.push({
        path: '/login',
        query: { redirect: currentPath }
      });
    }
  } catch (error) {
    console.error('处理未授权错误失败:', error);
    // 如果清理失败，至少要跳转到登录页
    router.push('/login');
  }
};

// 创建请求实例
const instance = axios.create({
  baseURL: import.meta.env.VITE_GLOB_DOMAIN_URL,
  // 指定请求超时的毫秒数
  timeout: 10000,
  // 表示跨域请求时是否需要使用凭证
  withCredentials: true,
  // 设置默认Content-Type
  headers: {
    'Content-Type': 'application/json;charset=UTF-8'
  }
});

// 请求拦截器处理
const requestInterceptors = (config, options = {}) => {
  // 请求之前处理config
  const userStore = useUserStore();
  const { token } = userStore;

  if (token && config?.requestOptions?.withToken !== false) {
    // jwt token
    config.headers.Authorization = options.authenticationScheme
      ? `${options.authenticationScheme} ${token}`
      : `Bearer ${token}`;
  }

  // 是否需要防止数据重复提交
  const isRepeatSubmit = config?.requestOptions?.repeatSubmit === false;
  // 是否需要加密
  const withEncrypt = !!config?.requestOptions?.withEncrypt;

  if (!isRepeatSubmit && (config.method.toUpperCase() === 'POST' || config.method.toUpperCase() === 'PUT')) {
    const requestObj = {
      url: config.url,
      data: typeof config.data === 'object' ? JSON.stringify(config.data) : config.data,
      time: new Date().getTime(),
    };
    const sessionObj = cache.session.getJSON('sessionObj');
    if (sessionObj === undefined || sessionObj === null || sessionObj === '') {
      cache.session.setJSON('sessionObj', requestObj);
    } else {
      const sUrl = sessionObj.url; // 请求地址
      const sData = sessionObj.data; // 请求数据
      const sTime = sessionObj.time; // 请求时间
      const interval = 1000; // 间隔时间(ms)，小于此时间视为重复提交
      if (sData === requestObj.data && requestObj.time - sTime < interval && sUrl === requestObj.url) {
        const message = '数据正在处理，请勿重复提交';
        console.warn(`[${sUrl}]: ${message}`);
        throw new Error(message);
      }
      cache.session.setJSON('sessionObj', requestObj);
    }
  }

  if (import.meta.env.VITE_APP_ENCRYPT === 'true') {
    // 当开启参数加密
    if (withEncrypt && (config.method === 'post' || config.method === 'put')) {
      // 生成一个 AES 密钥
      const aesKey = generateAesKey();
      config.headers[encryptHeader] = encrypt(encryptBase64(aesKey));

      // 加密数据 - 直接将加密后的内容作为请求体
      const dataToEncrypt = typeof config.data === 'object'
        ? JSON.stringify(config.data)
        : config.data;
      config.data = encryptWithAes(dataToEncrypt, aesKey);

      // 确保Content-Type是application/json
      config.headers['Content-Type'] = 'application/json;charset=UTF-8';
    }
  }

  // 设置语言头 - 使用下划线格式以匹配后端解析逻辑
  config.headers[contentLanguage] = config.headers[contentLanguage] || 'zh_CN';

  // 设置Content-Type为application/json
  if (config.method === 'post' || config.method === 'put') {
    config.headers['Content-Type'] = config.headers['Content-Type'] || 'application/json;charset=UTF-8';
  }

  return config;
};

// 前置拦截器（发起请求之前的拦截）
instance.interceptors.request.use(
  (config) => {
    return requestInterceptors(config, { authenticationScheme: 'Bearer' });
  },
  (error) => {
    return Promise.reject(error);
  },
);

// 处理请求数据。如果数据不是预期格式，可直接抛出错误
const transformRequestHook = (res, options = {}) => {
  const { isTransformResponse = true, isReturnNativeResponse = false } = options;

  // 加密后的 AES 秘钥
  const keyStr = res.headers[encryptHeader];
  // 加密
  if (keyStr) {
    const data = res.data;
    // 1. 用RSA私钥解密响应头中的AES密钥
    const base64Str = decrypt(keyStr);
    // 2. base64 解码得到AES秘钥
    const aesKey = decryptBase64(base64Str.toString());
    // 3. 用AES密钥解密响应数据
    const decryptData = decryptWithAes(data, aesKey);
    // 4. 将解密结果(JSON字符串)转为JSON对象
    res.data = JSON.parse(decryptData);
  }

  // 如果不需要转换响应，直接返回原始响应
  if (isReturnNativeResponse) {
    return res;
  }

  // 如果不需要转换响应数据，返回响应数据
  if (!isTransformResponse) {
    return res.data;
  }

  const { data } = res;

  // 检查业务状态码（通常在 data.code 中）
  if (data && data.code && data.code !== 200) {
    MessagePlugin.error(data.msg || data.message || '请求失败');
    return Promise.reject(new Error(data.msg || data.message || '请求失败'));
  }

  return data;
};

// 后置拦截器（获取到响应时的拦截）
instance.interceptors.response.use(
  (response) => {
    return transformRequestHook(response, {
      isTransformResponse: true,
      isReturnNativeResponse: false
    });
  },
  (error) => {
    const { response } = error;

    // 处理 HTTP 状态码错误
    if (response) {
      const { status, data } = response;
      let errorMessage = '请求失败';

      switch (status) {
        case 401:
          errorMessage = '登录已过期，请重新登录';
          // 处理登录过期逻辑
          handleUnauthorized();
          break;
        case 403:
          errorMessage = '拒绝访问';
          break;
        case 404:
          errorMessage = '请求地址不存在';
          break;
        case 500:
          errorMessage = '服务器内部错误';
          break;
        default:
          errorMessage = data?.msg || data?.message || `请求失败 (${status})`;
      }

      MessagePlugin.error(errorMessage);
    } else {
      // 网络错误或请求超时
      MessagePlugin.error(error.message || '网络连接失败');
    }

    console.error('Request Error:', error);
    return Promise.reject(error);
  },
);

// 导出常用函数

/**
 * @param {string} url
 * @param {object} data
 * @param {object} params
 */
export const post = (url, data = {}, params = {}) => {
  // 为POST请求自动添加时间戳参数，防止缓存
  const paramsWithTimestamp = {
    ...params,
    _t: Date.now()
  };

  return instance({
    method: 'post',
    url,
    data,
    params: paramsWithTimestamp,
  });
};

/**
 * @param {string} url
 * @param {object} params
 */
export const get = (url, params = {}) => {
  // 为GET请求自动添加时间戳参数，防止缓存
  const paramsWithTimestamp = {
    ...params,
    _t: Date.now()
  };

  return instance({
    method: 'get',
    url,
    params: paramsWithTimestamp,
  });
};

/**
 * @param {string} url
 * @param {object} data
 * @param {object} params
 */
export const put = (url, data = {}, params = {}) => {
  return instance({
    method: 'put',
    url,
    params,
    data,
  });
};

/**
 * @param {string} url
 * @param {object} params
 */
export const _delete = (url, params = {}) => {
  return instance({
    method: 'delete',
    url,
    params,
  });
};

// 创建统一的 request 对象，支持链式调用
export const request = {
  /**
   * GET 请求
   * @param {Object} config 请求配置
   * @param {Object} options 额外选项
   * @returns {Promise}
   */
  get(config, options = {}) {
    const mergedConfig = {
      method: 'get',
      ...config,
      requestOptions: {
        withToken: true,
        withEncrypt: false,
        repeatSubmit: true,
        ...config.requestOptions,
        ...options
      }
    };

    // 为GET请求自动添加时间戳参数，防止缓存
    if (mergedConfig.params) {
      mergedConfig.params._t = Date.now();
    } else {
      mergedConfig.params = { _t: Date.now() };
    }

    return instance(mergedConfig);
  },

  /**
   * POST 请求
   * @param {Object} config 请求配置
   * @param {Object} options 额外选项
   * @returns {Promise}
   */
  post(config, options = {}) {
    const mergedConfig = {
      method: 'post',
      ...config,
      requestOptions: {
        withToken: true,
        withEncrypt: false,
        repeatSubmit: true,
        ...config.requestOptions,
        ...options
      }
    };

    // 为POST请求自动添加时间戳参数，防止缓存
    if (mergedConfig.params) {
      mergedConfig.params._t = Date.now();
    } else {
      mergedConfig.params = { _t: Date.now() };
    }

    return instance(mergedConfig);
  },

  /**
   * PUT 请求
   * @param {Object} config 请求配置
   * @param {Object} options 额外选项
   * @returns {Promise}
   */
  put(config, options = {}) {
    return instance({
      method: 'put',
      ...config,
      requestOptions: {
        withToken: true,
        withEncrypt: false,
        repeatSubmit: true,
        ...config.requestOptions,
        ...options
      }
    });
  },

  /**
   * DELETE 请求
   * @param {Object} config 请求配置
   * @param {Object} options 额外选项
   * @returns {Promise}
   */
  delete(config, options = {}) {
    return instance({
      method: 'delete',
      ...config,
      requestOptions: {
        withToken: true,
        withEncrypt: false,
        repeatSubmit: true,
        ...config.requestOptions,
        ...options
      }
    });
  },
};

export default instance;
