/**
 * Blob对象转File对象
 * @param {Blob} blob
 * @param {string} fileName
 * @param {string} fileMimeType
 * @returns {File}
 */
export function blobToFile(blob, fileName, fileMimeType) {
  // 创建一个新的File对象
  // 在创建File对象时，需要使用Blob的slice方法来克隆Blob
  // 因为File构造函数接受的不是Blob对象，而是一个Blob对象数组（或者是一个由Blob对象构成的Array-Like对象）
  const newBlob = blob.slice(0, blob.size, fileMimeType);
  return new File([newBlob], fileName, {
    type: fileMimeType,
    lastModified: new Date().getTime(),
  });
}

/**
 * 提取http链接文件后缀名
 * @param {string} http
 * @returns {string}
 */
export function getHttpFileSuffix(http) {
  const index = http.indexOf('?');
  if (index !== -1) {
    http = http.substring(0, http.indexOf('?'));
  }
  return http.substring(http.lastIndexOf('.') + 1);
}

/**
 * 提取http链接文件名
 * @param {string} http
 * @returns {string}
 */
export function getHttpFileName(http) {
  const index = http.indexOf('?');
  if (index !== -1) {
    http = http.substring(0, http.indexOf('?'));
  }
  return http.substring(http.lastIndexOf('/') + 1);
}
