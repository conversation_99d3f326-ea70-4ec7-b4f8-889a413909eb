/**
 * 将秒转换为可读的时间格式
 * @param {number} seconds - 秒数
 * @returns {string} 格式化后的时间字符串
 */
export const formatDuration = (seconds) => {
  if (!seconds || seconds <= 0) return '0分钟'
  
  const hours = Math.floor(seconds / 3600)
  const minutes = Math.floor((seconds % 3600) / 60)
  
  if (hours > 0) {
    if (minutes > 0) {
      return `${hours}小时${minutes}分钟`
    } else {
      return `${hours}小时`
    }
  } else {
    return `${minutes}分钟`
  }
}

// 使用示例：
// formatDuration(3600)     // "1小时"
// formatDuration(3900)     // "1小时5分钟"
// formatDuration(1800)     // "30分钟"
// formatDuration(60)       // "1分钟"
// formatDuration(0)        // "0分钟"
