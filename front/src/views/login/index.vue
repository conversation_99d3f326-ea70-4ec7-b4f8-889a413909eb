<template>
  <div class="login-container">
    <!-- 全屏加载动画 -->
    <div v-if="isFullScreenLoading" class="fullscreen-loading">
      <div class="loading-backdrop"></div>
      <div class="loading-content">
        <div class="loading-spinner">
          <div class="spinner-ring"></div>
          <div class="spinner-ring"></div>
          <div class="spinner-ring"></div>
        </div>
        <div class="loading-text">
          <h3 class="loading-title">正在登录中...</h3>
          <p class="loading-subtitle">请稍候，正在为您验证身份</p>
        </div>
      </div>
    </div>

    <!-- 背景装饰 -->
    <div class="background-decoration">
      <div class="decoration-circle"></div>
      <div class="decoration-wave"></div>
    </div>

    <!-- 登录卡片 -->
    <div class="login-card">
      <!-- Logo和标题 -->
      <div class="login-header">
        <div class="logo">
          <t-icon name="home" class="logo-icon" />
        </div>
        <h1 class="title">家政帮手</h1>
        <p class="subtitle">专业家政服务，让生活更美好</p>
      </div>

      <!-- 登录表单 -->
      <div class="login-form">
        <!-- 微信登录按钮 -->
        <t-button
          theme="primary"
          size="large"
          block
          :loading="isLoading"
          @click="handleWeChatLogin"
          class="wechat-login-btn"
        >
          <t-icon name="logo-wechat" class="btn-icon" />
          <span v-if="!isLoading">微信登录</span>
          <span v-else>正在跳转...</span>
        </t-button>

        <!-- 登录提示 -->
        <p class="login-tip">
          点击按钮将跳转到微信授权页面完成登录
        </p>

        <!-- 服务条款 -->
        <div class="terms">
          <p class="terms-text">
            登录即表示您同意
            <a href="#" class="terms-link">《服务条款》</a>
            和
            <a href="#" class="terms-link">《隐私政策》</a>
          </p>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref, onMounted } from 'vue'
import { useRouter, useRoute } from 'vue-router'
import { useUserStore } from '@/store/modules/user.js'
import { GetWeChatParams, WxLogin } from '@/api/wxLogin.js'
import { MessagePlugin } from 'tdesign-vue-next'

const router = useRouter()
const route = useRoute()
const userStore = useUserStore()

// 响应式数据
const isLoading = ref(false)
const isFullScreenLoading = ref(false)

// 处理微信登录
const handleWeChatLogin = async () => {
  try {
    isLoading.value = true
    const response = await GetWeChatParams()
    if (response.code !== 200) {
      await MessagePlugin.error(response.msg || '获取微信登录链接失败')
      return
    }
    // 跳转到微信授权页面
    window.location.href = response.data
  } catch (error) {
    console.error('微信登录失败:', error)
    await MessagePlugin.error('网络错误，请稍后重试')
  } finally {
    isLoading.value = false
  }
}

// 检查登录状态
const checkLoginStatus = async () => {
  const code = route.query.code
  if (code) {
    try {
      // 显示全屏加载动画
      isFullScreenLoading.value = true
      isLoading.value = true
      const response = await WxLogin(code)

      if (response.code === 200) {

        // 登录成功，保存token
        userStore.$patch({ token: response.data.access_token })

        // 获取用户信息
        await userStore.getUserInfo()


        // 获取重定向地址，如果没有则跳转到首页
        const redirect = route.query.redirect || '/'

        // 跳转到目标页面
        await router.push(redirect)

        // 显示成功消息
        await MessagePlugin.success("微信登录成功！欢迎回来 🎉")

      } else {

        await MessagePlugin.error(response.msg || '登录失败，请稍后再试')
      }
    } catch (error) {
      console.error('微信登录失败:', error)


      await MessagePlugin.error('登录失败，请稍后重试')
    } finally {
      isLoading.value = false
      isFullScreenLoading.value = false
    }
  }
}

// 组件挂载时检查登录状态
onMounted(() => {
  checkLoginStatus()
})
</script>

<style scoped lang="less">
// 全屏加载动画
.fullscreen-loading {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  z-index: 9999;
  display: flex;
  align-items: center;
  justify-content: center;
}

.loading-backdrop {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(255, 255, 255, 0.95);
  backdrop-filter: blur(8px);
  animation: fadeIn 0.3s ease-out;
}

.loading-content {
  position: relative;
  z-index: 1;
  text-align: center;
  animation: slideUp 0.5s ease-out;
}

.loading-spinner {
  position: relative;
  width: 80px;
  height: 80px;
  margin: 0 auto 2rem;
}

.spinner-ring {
  position: absolute;
  width: 100%;
  height: 100%;
  border: 3px solid transparent;
  border-radius: 50%;
  animation: spin 1.5s linear infinite;

  &:nth-child(1) {
    border-top-color: #22c55e;
    animation-delay: 0s;
  }

  &:nth-child(2) {
    border-right-color: #10b981;
    animation-delay: -0.5s;
    width: 70%;
    height: 70%;
    top: 15%;
    left: 15%;
  }

  &:nth-child(3) {
    border-bottom-color: #059669;
    animation-delay: -1s;
    width: 40%;
    height: 40%;
    top: 30%;
    left: 30%;
  }
}

.loading-text {
  color: #374151;
}

.loading-title {
  font-size: 1.25rem;
  font-weight: 600;
  margin-bottom: 0.5rem;
  color: #1f2937;
}

.loading-subtitle {
  font-size: 0.875rem;
  color: #6b7280;
  margin: 0;
}

@keyframes fadeIn {
  from {
    opacity: 0;
  }
  to {
    opacity: 1;
  }
}

@keyframes slideUp {
  from {
    opacity: 0;
    transform: translateY(20px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes spin {
  from {
    transform: rotate(0deg);
  }
  to {
    transform: rotate(360deg);
  }
}

.login-container {
  min-height: 100vh;
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 1rem;
  position: relative;
  background: linear-gradient(135deg, #f8fafc 0%, #e2e8f0 100%);
  overflow: hidden;
}

// 背景装饰
.background-decoration {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  pointer-events: none;
  z-index: 1;
}

.decoration-circle {
  position: absolute;
  width: 400px;
  height: 400px;
  border-radius: 50%;
  background: linear-gradient(135deg, rgba(34, 197, 94, 0.1), rgba(16, 185, 129, 0.05));
  top: -200px;
  right: -200px;
  animation: float 6s ease-in-out infinite;
}

.decoration-wave {
  position: absolute;
  bottom: 0;
  left: 0;
  width: 100%;
  height: 100px;
  background: linear-gradient(90deg, rgba(34, 197, 94, 0.08), rgba(16, 185, 129, 0.03));
  clip-path: polygon(0 50%, 100% 80%, 100% 100%, 0% 100%);
}

@keyframes float {
  0%, 100% {
    transform: translateY(0px);
  }
  50% {
    transform: translateY(-20px);
  }
}

// 登录卡片
.login-card {
  position: relative;
  z-index: 2;
  background: white;
  border-radius: 16px;
  padding: 3rem 2.5rem;
  box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);
  width: 100%;
  max-width: 400px;
  text-align: center;
}

// 登录头部
.login-header {
  margin-bottom: 2.5rem;
}

.logo {
  width: 4rem;
  height: 4rem;
  background: linear-gradient(135deg, #1890ff, #096dd9);
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  margin: 0 auto 1.5rem;
  box-shadow: 0 8px 16px rgba(24, 144, 255, 0.3);

  .logo-icon {
    font-size: 2rem;
    color: white;
  }
}

.title {
  font-size: 1.75rem;
  font-weight: bold;
  color: #1f2937;
  margin-bottom: 0.5rem;
}

.subtitle {
  color: #6b7280;
  font-size: 0.875rem;
  line-height: 1.5;
}

// 登录表单
.login-form {
  display: flex;
  flex-direction: column;
  gap: 1.5rem;
}

.wechat-login-btn {
  height: 3.5rem;
  font-size: 1rem;
  font-weight: 600;
  border-radius: 12px;
  background: linear-gradient(135deg, #07c160, #06ad56);
  border: none;
  box-shadow: 0 4px 12px rgba(7, 193, 96, 0.3);
  transition: all 0.3s ease;

  &:hover {
    transform: translateY(-2px);
    box-shadow: 0 6px 16px rgba(7, 193, 96, 0.4);
  }

  &:active {
    transform: translateY(0);
  }

  .btn-icon {
    margin-right: 0.5rem;
    font-size: 1.25rem;
  }
}

.login-tip {
  font-size: 0.875rem;
  color: #6b7280;
  line-height: 1.5;
}

// 服务条款
.terms {
  padding-top: 1rem;
  border-top: 1px solid #f3f4f6;
}

.terms-text {
  font-size: 0.75rem;
  color: #9ca3af;
  line-height: 1.5;
}

.terms-link {
  color: #1890ff;
  text-decoration: none;

  &:hover {
    text-decoration: underline;
  }
}

// 响应式设计
@media (max-width: 480px) {
  .login-card {
    padding: 2rem 1.5rem;
    margin: 1rem;
  }

  .logo {
    width: 3rem;
    height: 3rem;
    margin-bottom: 1rem;

    .logo-icon {
      font-size: 1.5rem;
    }
  }

  .title {
    font-size: 1.5rem;
  }

  .wechat-login-btn {
    height: 3rem;
    font-size: 0.875rem;
  }
}

@media (max-width: 360px) {
  .login-container {
    padding: 0.5rem;
  }

  .login-card {
    padding: 1.5rem 1rem;
  }
}
</style>
