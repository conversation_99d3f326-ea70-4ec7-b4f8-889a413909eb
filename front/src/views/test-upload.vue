<template>
  <div class="test-upload-page">
    <h1>图片上传组件测试</h1>
    
    <div class="test-section">
      <h2>基本用法</h2>
      <ImageUpload
        v-model="basicImages"
        :limit="1"
        @change="handleBasicChange"
      />
      <p>当前值: {{ basicImages }}</p>
    </div>

    <div class="test-section">
      <h2>多图片上传</h2>
      <ImageUpload
        v-model="multipleImages"
        :limit="3"
        :file-size="10"
        @change="handleMultipleChange"
      />
      <p>当前值: {{ multipleImages }}</p>
    </div>

    <div class="test-section">
      <h2>启用压缩</h2>
      <ImageUpload
        v-model="compressImages"
        :limit="2"
        :compress-support="true"
        :compress-target-size="500"
        @change="handleCompressChange"
      />
      <p>当前值: {{ compressImages }}</p>
    </div>

    <div class="test-section">
      <h2>ID模式</h2>
      <ImageUpload
        v-model="idModeImages"
        mode="id"
        :limit="2"
        @change="handleIdModeChange"
      />
      <p>当前值: {{ idModeImages }}</p>
    </div>
  </div>
</template>

<script setup>
import { ref } from 'vue'
import ImageUpload from '@/components/ImageUpload/index.vue'

const basicImages = ref('')
const multipleImages = ref('')
const compressImages = ref('')
const idModeImages = ref('')

const handleBasicChange = (value) => {
  console.log('基本用法变化:', value)
}

const handleMultipleChange = (value) => {
  console.log('多图片变化:', value)
}

const handleCompressChange = (value) => {
  console.log('压缩模式变化:', value)
}

const handleIdModeChange = (value) => {
  console.log('ID模式变化:', value)
}
</script>

<style lang="less" scoped>
.test-upload-page {
  padding: 20px;
  max-width: 800px;
  margin: 0 auto;
}

.test-section {
  margin-bottom: 40px;
  padding: 20px;
  border: 1px solid #e5e7eb;
  border-radius: 8px;
  
  h2 {
    margin-top: 0;
    margin-bottom: 16px;
    color: #1f2937;
  }
  
  p {
    margin-top: 16px;
    padding: 8px;
    background-color: #f3f4f6;
    border-radius: 4px;
    font-family: monospace;
    word-break: break-all;
  }
}
</style>
