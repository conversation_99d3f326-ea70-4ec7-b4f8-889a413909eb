<template>
  <div class="home-service-website">
    <!-- Hero区域 -->
    <HeroSection />

    <!-- 服务项目 -->
    <ServiceSection />

    <!-- 服务流程 -->
    <ProcessSection />

    <!-- 服务团队 -->
    <TeamSection />

    <!-- 客户评价 -->
    <ReviewsSection />

    <!-- 联系我们 -->
    <ContactSection />
  </div>
</template>

<script setup>
import { ref, computed, onMounted, } from 'vue'
import {
  Phone,
  Calendar,
  Sparkles,
  Check,
  Star,
  MapPin,
  Clock,
  Users,
  Heart,
  Search,
  UserCheck,
  Award,
  Grid,
  Home,
  ChevronLeft,
  ChevronRight
} from 'lucide-vue-next'
import { GetWeChatParams, WxLogin } from '@/api/wxLogin.js'
import { MessagePlugin } from 'tdesign-vue-next';
import { useRouter, useRoute} from 'vue-router'
import { useUserStore } from '@/store/modules/user.js'
import HeroSection from './HeroSection.vue'
import ServiceSection from './ServiceSection.vue'
import ProcessSection from './ProcessSection.vue'
import TeamSection from './TeamSection.vue'
import ReviewsSection from './ReviewsSection.vue'
import ContactSection from './ContactSection.vue'

const route = useRoute()
const router = useRouter()
const userStore = useUserStore()

onMounted(async () => {

})









</script>

<style scoped>
* {
  margin: 0;
  padding: 0;
  box-sizing: border-box;
}

.home-service-website {
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
  line-height: 1.6;
  color: #333;
}

.container {
  max-width: 1400px;
  margin: 0 auto;
  padding: 0 20px;
}
















</style>
