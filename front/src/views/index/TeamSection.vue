<template>
  <!-- 服务团队 -->
  <section id="team" class="team-section">
    <div class="container">
      <div class="section-header">
        <h2 class="section-title">专业团队</h2>
        <p class="section-description">经过严格筛选和专业培训的服务人员</p>
      </div>

      <!-- 团队轮播容器 -->
      <div class="team-carousel">
        <!-- 左箭头 -->
        <button
          class="carousel-btn carousel-btn-prev"
          @click="prevSlide"
          :disabled="!canGoPrev"
          :class="{ 'disabled': !canGoPrev }"
        >
          <ChevronLeft class="carousel-icon" />
        </button>

        <!-- 团队成员网格 -->
        <div class="team-grid-container">
          <div class="team-grid">
            <div
              v-for="member in displayedTeamMembers"
              :key="member.id"
              class="team-card"
            >
              <div class="member-avatar">
                <span class="avatar-text">{{ member.name.charAt(0) }}</span>
              </div>
              <h3 class="member-name">{{ member.name }}</h3>
              <p class="member-role">{{ member.role }}</p>
              <p class="member-experience">{{ member.experience }}</p>
              <div class="member-skills">
                <span
                  v-for="skill in member.skills"
                  :key="skill"
                  class="skill-tag"
                >
                  {{ skill }}
                </span>
              </div>
              <div class="member-rating">
                <Star
                  v-for="i in 5"
                  :key="i"
                  class="star-icon"
                />
                <span class="rating-text">{{ member.rating }}</span>
              </div>
            </div>
          </div>
        </div>

        <!-- 右箭头 -->
        <button
          class="carousel-btn carousel-btn-next"
          @click="nextSlide"
          :disabled="!canGoNext"
          :class="{ 'disabled': !canGoNext }"
        >
          <ChevronRight class="carousel-icon" />
        </button>
      </div>

      <!-- 轮播指示器 -->
      <div class="carousel-indicators">
        <button
          v-for="(slide, index) in totalSlides"
          :key="index"
          class="indicator-dot"
          :class="{ 'active': index === currentSlide }"
          @click="goToSlide(index)"
        ></button>
      </div>
    </div>
  </section>
</template>

<script setup>
import { ref, computed } from 'vue'
import {
  Star,
  ChevronLeft,
  ChevronRight
} from 'lucide-vue-next'

// 团队成员数据
const allTeamMembers = ref([
  {
    id: 1,
    name: '李阿姨',
    role: '高级保洁师',
    experience: '8年家政经验',
    skills: ['家庭保洁', '深度清洁', '收纳整理'],
    rating: '4.9'
  },
  {
    id: 2,
    name: '王阿姨',
    role: '专业保姆',
    experience: '10年保姆经验',
    skills: ['老人护理', '儿童看护', '家务料理'],
    rating: '4.8'
  },
  {
    id: 3,
    name: '张阿姨',
    role: '金牌月嫂',
    experience: '12年月嫂经验',
    skills: ['新生儿护理', '产妇护理', '营养配餐'],
    rating: '5.0'
  },
  {
    id: 4,
    name: '刘阿姨',
    role: '育儿嫂',
    experience: '6年育儿经验',
    skills: ['婴幼儿护理', '早教启蒙', '辅食制作'],
    rating: '4.9'
  },
  {
    id: 5,
    name: '陈阿姨',
    role: '高级保洁师',
    experience: '5年家政经验',
    skills: ['家庭保洁', '玻璃清洁', '家具护理'],
    rating: '4.7'
  },
  {
    id: 6,
    name: '赵阿姨',
    role: '专业保姆',
    experience: '15年保姆经验',
    skills: ['老人陪护', '病患护理', '营养搭配'],
    rating: '4.9'
  },
  {
    id: 7,
    name: '孙阿姨',
    role: '金牌月嫂',
    experience: '10年月嫂经验',
    skills: ['产后恢复', '母乳指导', '婴儿按摩'],
    rating: '4.8'
  },
  {
    id: 8,
    name: '周阿姨',
    role: '育儿嫂',
    experience: '8年育儿经验',
    skills: ['幼儿教育', '营养配餐', '行为引导'],
    rating: '4.9'
  }
])

// 轮播相关状态
const currentSlide = ref(0)
const itemsPerSlide = 4

// 计算显示的团队成员
const displayedTeamMembers = computed(() => {
  const start = currentSlide.value * itemsPerSlide
  return allTeamMembers.value.slice(start, start + itemsPerSlide)
})

// 计算总页数
const totalSlides = computed(() => {
  return Math.ceil(allTeamMembers.value.length / itemsPerSlide)
})

// 轮播控制方法
const nextSlide = () => {
  if (currentSlide.value < totalSlides.value - 1) {
    currentSlide.value++
  }
}

const prevSlide = () => {
  if (currentSlide.value > 0) {
    currentSlide.value--
  }
}

// 跳转到指定页面
const goToSlide = (index) => {
  currentSlide.value = index
}

// 检查是否可以继续滑动
const canGoNext = computed(() => currentSlide.value < totalSlides.value - 1)
const canGoPrev = computed(() => currentSlide.value > 0)
</script>

<style scoped lang="less">
.container {
  max-width: 1400px;
  margin: 0 auto;
  padding: 0 20px;
}

.section-header {
  text-align: center;
  margin-bottom: 1.5rem;
}

.section-title {
  font-size: 2.5rem;
  font-weight: bold;
  color: #1f2937;
  margin-bottom: 1rem;
}

.section-description {
  font-size: 1.125rem;
  color: #6b7280;
  max-width: 700px;
  margin: 0 auto;
  line-height: 1.6;
}

/* 服务团队 */
.team-section {
  padding: 5rem 0;
  background: white;
}

/* 团队轮播容器 */
.team-carousel {
  display: flex;
  align-items: center;
  gap: 2rem;
  max-width: 1600px;
  margin: 0 auto;
  position: relative;
  padding: 0 1rem;
}

.team-grid-container {
  flex: 1;
  overflow: hidden;
  width: 100%;
}

.team-grid {
  display: grid;
  grid-template-columns: repeat(4, 1fr);
  gap: 1.5rem;
  width: 100%;
}

/* 轮播按钮 */
.carousel-btn {
  width: 3rem;
  height: 3rem;
  border-radius: 50%;
  border: none;
  background: white;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  transition: all 0.3s ease;
  flex-shrink: 0;
  z-index: 10;
}

.carousel-btn:hover:not(.disabled) {
  background: #3b82f6;
  transform: scale(1.1);
}

.carousel-btn:hover:not(.disabled) .carousel-icon {
  color: white;
}

.carousel-btn.disabled {
  opacity: 0.4;
  cursor: not-allowed;
  box-shadow: 0 2px 6px rgba(0, 0, 0, 0.1);
}

.carousel-icon {
  width: 1.5rem;
  height: 1.5rem;
  color: #6b7280;
}

/* 轮播指示器 */
.carousel-indicators {
  display: flex;
  justify-content: center;
  gap: 0.5rem;
  margin-top: 2rem;
}

.indicator-dot {
  width: 0.75rem;
  height: 0.75rem;
  border-radius: 50%;
  border: none;
  background: #d1d5db;
  cursor: pointer;
  transition: all 0.3s ease;
}

.indicator-dot.active {
  background: #3b82f6;
  transform: scale(1.2);
}

.indicator-dot:hover {
  background: #9ca3af;
}

.team-card {
  background: white;
  border-radius: 12px;
  padding: 2.5rem 2rem;
  text-align: center;
  box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
  border: 1px solid #e5e7eb;
  transition: transform 0.3s ease;
  width: 100%;
  min-height: 350px;
}

.team-card:hover {
  transform: translateY(-3px);
}

.member-avatar {
  width: 6rem;
  height: 6rem;
  background: linear-gradient(135deg, #3b82f6, #1d4ed8);
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  margin: 0 auto 1.5rem;
}

.avatar-text {
  color: white;
  font-size: 1.75rem;
  font-weight: bold;
}

.member-name {
  font-size: 1.375rem;
  font-weight: 600;
  color: #1f2937;
  margin-bottom: 0.75rem;
}

.member-role {
  color: #3b82f6;
  font-weight: 500;
  font-size: 1.125rem;
  margin-bottom: 0.75rem;
}

.member-experience {
  color: #6b7280;
  font-size: 1rem;
  margin-bottom: 1.25rem;
}

.member-skills {
  display: flex;
  flex-wrap: wrap;
  gap: 0.75rem;
  justify-content: center;
  margin-bottom: 1.25rem;
}

.skill-tag {
  background: #e0f2fe;
  color: #0369a1;
  padding: 0.375rem 1rem;
  border-radius: 20px;
  font-size: 0.875rem;
  font-weight: 500;
}

.member-rating {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 0.5rem;
}

.star-icon {
  width: 1rem;
  height: 1rem;
  color: #fbbf24;
  fill: currentColor;
}

.rating-text {
  color: #6b7280;
  font-weight: 500;
}

/* 响应式设计 */
@media (max-width: 1200px) {
  .team-grid {
    grid-template-columns: repeat(2, 1fr);
    gap: 1.25rem;
  }

  .team-carousel {
    gap: 1rem;
    max-width: 1000px;
  }

  .team-card {
    min-height: 300px;
  }

  .carousel-btn {
    width: 2.5rem;
    height: 2.5rem;
  }

  .carousel-icon {
    width: 1.25rem;
    height: 1.25rem;
  }
}

@media (max-width: 768px) {
  .section-title {
    font-size: 2rem;
  }

  .team-grid {
    grid-template-columns: 1fr;
    gap: 1.5rem;
  }

  .team-carousel {
    flex-direction: column;
    gap: 1.5rem;
    max-width: 400px;
  }

  .team-card {
    min-height: 280px;
    padding: 2rem 1.5rem;
  }

  .carousel-btn {
    width: 2rem;
    height: 2rem;
    position: static;
  }

  .carousel-icon {
    width: 1rem;
    height: 1rem;
  }

  .carousel-btn-prev,
  .carousel-btn-next {
    order: 2;
  }

  .team-grid-container {
    order: 1;
  }

  .carousel-indicators {
    order: 3;
    margin-top: 1rem;
  }
}
</style>
