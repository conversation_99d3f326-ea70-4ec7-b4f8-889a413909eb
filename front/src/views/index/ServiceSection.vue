<template>
  <!-- 服务项目 -->
  <section id="services" class="services-section">
    <div class="container">
      <div class="section-header">
        <h2 class="section-title">我们的服务项目</h2>
        <p class="section-description">提供全方位的专业家政服务，满足您的不同需求，让您的生活更加舒适便捷</p>
      </div>
      <div class="services-grid">
        <div
          v-for="service in services"
          :key="service.id"
          class="service-card"
        >
          <div class="service-image" :class="service.imageClass">
            <img v-if="service.image" :src="service.image" :alt="service.title" class="service-img" />
          </div>
          <div class="service-content">
            <h3 class="service-title">{{ service.title }}</h3>
            <p class="service-description">{{ service.description }}</p>
            <div class="service-tags">
              <span v-for="tag in parseTags(service.tag)" :key="tag" class="tag-item">
                {{ tag }}
              </span>
            </div>
            <div class="service-footer">
              <div class="service-price">
                <span class="price">¥{{ service.price }}</span>
                <span class="price-unit">{{ service.unit }}</span>
              </div>
              <button class="service-btn" @click="$router.push('/my-bookings')">
                了解详情
                <ChevronRight class="btn-arrow" />
              </button>
            </div>
          </div>
        </div>
      </div>
    </div>
  </section>
</template>

<script setup>
import { ref, onMounted } from 'vue'
import { ChevronRight } from 'lucide-vue-next'

// 解析tag字符串的方法
const parseTags = (tagString) => {
  if (!tagString) return []
  try {
    // 如果是JSON字符串格式，解析它
    if (tagString.startsWith('[') && tagString.endsWith(']')) {
      return JSON.parse(tagString)
    }
    // 如果是逗号分隔的字符串
    if (tagString.includes(',')) {
      return tagString.split(',').map(tag => tag.trim())
    }
    // 如果是单个标签
    return [tagString]
  } catch (error) {
    console.warn('解析tag失败:', error)
    return []
  }
}
import { ListServe } from '@/api/serve.js'
import { MessagePlugin } from 'tdesign-vue-next';


const listServe = async () => {
  const response = await ListServe({
    pageNum: 1,
    pageSize: 10,
    isPush: 1
  })
  if(response.code !==200){
    await MessagePlugin.error(response.msg || "加载服务项目失败")
    return
  }
  services.value = response.rows
}
onMounted(async () => {
  await listServe()
})

// 服务项目数据
const services = ref([
  {
    id: 1,
    title: '日常保洁',
    description: '包括地面清洁、家具擦拭、厨房清洁、卫生间清洁等基础保洁服务，让您的家保持整洁。',
    price: '128',
    unit: '/次起',
    imageClass: 'image-daily',
    image: 'http://**************:9154/ruoyi/2025/07/19/aa946eb5e9ab4524afc63a120f0be164.png',
    tag: '["日常保洁","深度清洁","开荒保洁"]'
  },
  {
    id: 2,
    title: '深度清洁',
    description: '针对长期积累的污渍进行彻底清洁，包含家电清洁、窗户清洁等深度清洁服务',
    price: '268',
    unit: '/次起',
    imageClass: 'image-deep',
    image: 'http://**************:9154/ruoyi/2025/07/19/aa946eb5e9ab4524afc63a120f0be164.png',
    tag: '["深度清洁","家电清洁","除菌消毒"]'
  },
  {
    id: 3,
    title: '搬家保洁',
    description: '针对新房或搬家后的全面清洁服务，包含装修后清洁、全屋深度清洁等专业服务',
    price: '398',
    unit: '/次起',
    imageClass: 'image-moving',
    image: 'http://**************:9154/ruoyi/2025/07/19/aa946eb5e9ab4524afc63a120f0be164.png',
    tag: '["搬家保洁","装修清洁","全屋清洁"]'
  },
  {
    id: 4,
    title: '办公室清洁',
    description: '为企业提供专业的办公环境清洁服务，包含办公桌椅、地面、玻璃等全方位清洁',
    price: '458',
    unit: '/次起',
    imageClass: 'image-office',
    image: 'http://**************:9154/ruoyi/2025/07/19/aa946eb5e9ab4524afc63a120f0be164.png',
    tag: '["办公清洁","商业保洁","企业服务"]'
  },
  {
    id: 5,
    title: '玻璃清洁',
    description: '专业的玻璃清洁服务，包含室内外玻璃、镜面等清洁，让您的视野更加清晰明亮',
    price: '188',
    unit: '/次起',
    imageClass: 'image-glass',
    image: 'http://**************:9154/ruoyi/2025/07/19/aa946eb5e9ab4524afc63a120f0be164.png',
    tag: '["玻璃清洁","镜面清洁","专业清洁"]'
  },
  {
    id: 6,
    title: '开荒保洁',
    description: '针对新装修房屋的首次清洁服务，清除装修垃圾、粉尘等，为您打造洁净的居住环境',
    price: '588',
    unit: '/次起',
    imageClass: 'image-initial',
    image: 'http://**************:9154/ruoyi/2025/07/19/aa946eb5e9ab4524afc63a120f0be164.png',
    tag: '["开荒保洁","新房清洁","装修后清洁"]'
  }
])
</script>

<style scoped lang="less">
.container {
  max-width: 1400px;
  margin: 0 auto;
  padding: 0 20px;
}

/* 服务项目 */
.services-section {
  padding: 2rem 0;
  background: #f8fafc;
}

.section-header {
  text-align: center;
  margin-bottom: 1.5rem;
}

.section-title {
  font-size: 2.5rem;
  font-weight: bold;
  color: #1f2937;
  margin-bottom: 1rem;
}

.section-description {
  font-size: 1.125rem;
  color: #6b7280;
  max-width: 700px;
  margin: 0 auto;
  line-height: 1.6;
}

.services-grid {
  display: grid;
  grid-template-columns: repeat(3, 1fr);
  gap: 1rem;
  max-width: 1200px;
  margin: 0 auto;
}

.service-card {
  background: white;
  border-radius: 12px;
  overflow: hidden;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.08);
  border: 1px solid #e5e7eb;
  transition: all 0.3s ease;
  display: flex;
  flex-direction: column;
  height: 420px;
}

.service-card:hover {
  transform: translateY(-5px);
  box-shadow: 0 12px 30px rgba(0, 0, 0, 0.15);
}

.service-image {
  position: relative;
  height: 200px;
  overflow: hidden;
  background: linear-gradient(135deg, #f1f5f9 0%, #e2e8f0 100%);
  transition: transform 0.3s ease;
  display: flex;
  align-items: center;
  justify-content: center;
}

.service-img {
  width: 100%;
  height: 100%;
  object-fit: cover;
  transition: transform 0.3s ease;
}

.service-card:hover .service-image {
  transform: scale(1.02);
}

.service-card:hover .service-img {
  transform: scale(1.05);
}

/* 服务图片背景样式 */
.service-image.image-daily {
  background: linear-gradient(135deg, #dbeafe 0%, #bfdbfe 50%, #93c5fd 100%);
}

.service-image.image-deep {
  background: linear-gradient(135deg, #fed7aa 0%, #fdba74 50%, #fb923c 100%);
}

.service-image.image-moving {
  background: linear-gradient(135deg, #dcfce7 0%, #bbf7d0 50%, #86efac 100%);
}

.service-image.image-office {
  background: linear-gradient(135deg, #e9d5ff 0%, #ddd6fe 50%, #c4b5fd 100%);
}

.service-image.image-glass {
  background: linear-gradient(135deg, #e0f2fe 0%, #bae6fd 50%, #7dd3fc 100%);
}

.service-image.image-initial {
  background: linear-gradient(135deg, #fef3c7 0%, #fde68a 50%, #fcd34d 100%);
}

.service-content {
  padding: 1.5rem;
  flex: 1;
  display: flex;
  flex-direction: column;
}

.service-title {
  font-size: 1.25rem;
  font-weight: 600;
  color: #1f2937;
  margin-bottom: 0.75rem;
}

.service-description {
  color: #6b7280;
  line-height: 1.5;
  font-size: 1rem;
  margin-bottom: 1rem;
  flex: 1;
}

.service-tags {
  margin-bottom: 1.5rem;
}

.tag-item {
  display: inline-block;
  background: #f9fafb;
  color: #374151;
  padding: 0.25rem 0.75rem;
  border-radius: 12px;
  font-size: 0.75rem;
  margin-right: 0.5rem;
  margin-bottom: 0.5rem;
  border: 1px solid #e5e7eb;
  transition: all 0.3s ease;
}

.tag-item:hover {
  background: #374151;
  color: white;
  transform: translateY(-1px);
}

.service-footer {
  display: flex;
  align-items: center;
  justify-content: space-between;
  margin-top: auto;
}

.service-price {
  display: flex;
  align-items: baseline;
  gap: 0.25rem;
}

.price {
  font-size: 1.5rem;
  font-weight: bold;
  color: #3b82f6;
  line-height: 1;
}

.price-unit {
  color: #6b7280;
  font-size: 0.75rem;
}

.service-btn {
  background: transparent;
  color: #3b82f6;
  border: 1px solid #3b82f6;
  padding: 0.5rem 1rem;
  border-radius: 6px;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.3s ease;
  font-size: 0.875rem;
  display: flex;
  align-items: center;
  gap: 0.25rem;
}

.service-btn:hover {
  background: #3b82f6;
  color: white;
}

.btn-arrow {
  width: 1rem;
  height: 1rem;
  transition: transform 0.3s ease;
}

.service-btn:hover .btn-arrow {
  transform: translateX(2px);
}

/* 响应式设计 */
@media (max-width: 1200px) {
  .services-grid {
    grid-template-columns: repeat(2, 1fr);
    gap: 1.5rem;
  }
}

@media (max-width: 768px) {
  .section-title {
    font-size: 2rem;
  }

  .services-grid {
    grid-template-columns: 1fr;
    gap: 1.5rem;
  }

  .service-card {
    height: auto;
  }

  .service-image {
    height: 180px;
  }
}
</style>
