<template>
  <!-- Hero区域 -->
  <section class="hero-section">
    <div class="container">
      <div class="hero-content">
        <div class="hero-text">
          <h1 class="hero-title">
            专业家政服务
            <span class="highlight">让生活更美好</span>
          </h1>
          <p class="hero-description">
            提供专业的家庭清洁、保姆、月嫂、育儿嫂等全方位家政服务
            <br>让您享受品质生活，我们为您分担家务烦恼
          </p>
          <div class="hero-buttons">
            <button class="btn-primary">
              <Phone class="btn-icon" />
              立即咨询
            </button>
            <button class="btn-secondary" @click="$router.push('/my-bookings')">
              <Calendar class="btn-icon" />
              我的预约
            </button>
          </div>
          <div class="hero-stats">
            <div class="stat-item">
              <div class="stat-number">10000+</div>
              <div class="stat-label">服务家庭</div>
            </div>
            <div class="stat-item">
              <div class="stat-number">500+</div>
              <div class="stat-label">专业阿姨</div>
            </div>
            <div class="stat-item">
              <div class="stat-number">98%</div>
              <div class="stat-label">满意度</div>
            </div>
          </div>
        </div>
        <div class="hero-image">
          <div class="image-container">
            <img
              alt="专业家政服务"
              src="https://design.gemcoder.com/api/searchImage?query=professional%20housekeeper%20cleaning%20modern%20home,%20organized%20living%20room,%20bright%20and%20clean%20environment&width=600&height=400"
            />
          </div>
          <div class="bg-decoration bg-decoration-1"></div>
          <div class="bg-decoration bg-decoration-2"></div>
        </div>
      </div>
    </div>
  </section>
</template>

<script setup>
import { Phone, Calendar } from 'lucide-vue-next'
</script>

<style scoped lang="less">
.container {
  max-width: 1400px;
  margin: 0 auto;
  padding: 0 20px;
}

/* Hero区域 */
.hero-section {
  padding: 5rem 0;
  background: linear-gradient(135deg, #f8fafc 0%, #e2e8f0 100%);
}

.hero-content {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 4rem;
  align-items: center;
}

.hero-title {
  font-size: 3rem;
  font-weight: bold;
  color: #1f2937;
  margin-bottom: 1.5rem;
  line-height: 1.2;
}

.highlight {
  color: #3b82f6;
  display: block;
}

.hero-description {
  font-size: 1.25rem;
  color: #6b7280;
  margin-bottom: 2rem;
  line-height: 1.6;
}

.hero-buttons {
  display: flex;
  gap: 1rem;
  margin-bottom: 3rem;
}

.btn-primary {
  background: #3b82f6;
  color: white;
  border: none;
  padding: 1rem 2rem;
  border-radius: 8px;
  font-size: 1.125rem;
  font-weight: 500;
  cursor: pointer;
  display: flex;
  align-items: center;
  gap: 0.5rem;
  transition: background-color 0.3s ease;
}

.btn-primary:hover {
  background: #2563eb;
}

.btn-secondary {
  background: transparent;
  color: #3b82f6;
  border: 2px solid #3b82f6;
  padding: 1rem 2rem;
  border-radius: 8px;
  font-size: 1.125rem;
  font-weight: 500;
  cursor: pointer;
  display: flex;
  align-items: center;
  gap: 0.5rem;
  transition: all 0.3s ease;
}

.btn-secondary:hover {
  background: #3b82f6;
  color: white;
}

.btn-icon {
  width: 1.25rem;
  height: 1.25rem;
}

.hero-stats {
  display: flex;
  gap: 2rem;
}

.stat-item {
  text-align: center;
}

.stat-number {
  font-size: 2rem;
  font-weight: bold;
  color: #3b82f6;
  margin-bottom: 0.25rem;
}

.stat-label {
  font-size: 0.875rem;
  color: #6b7280;
}

.hero-image {
  width: 100%;
  position: relative;
  display: flex;
  justify-content: center;
}

.image-container {
  position: relative;
  z-index: 10;
  border-radius: 16px;
  overflow: hidden;
  box-shadow: 0 25px 50px -12px rgba(0, 0, 0, 0.25);
  transform: rotate(2deg);
  transition: transform 0.5s ease;
  max-width: 500px;
  width: 100%;
}

.image-container:hover {
  transform: rotate(0deg);
}

.image-container img {
  width: 100%;
  height: auto;
  object-fit: cover;
}

.bg-decoration {
  position: absolute;
  border-radius: 50%;
  filter: blur(48px);
  z-index: 0;
}

.bg-decoration-1 {
  top: 40px;
  right: -40px;
  width: 160px;
  height: 160px;
  background-color: rgba(59, 130, 246, 0.2);
}

.bg-decoration-2 {
  bottom: -40px;
  left: -40px;
  width: 240px;
  height: 240px;
  background-color: rgba(59, 130, 246, 0.15);
}

/* 响应式设计 */
@media (max-width: 768px) {
  .hero-content {
    grid-template-columns: 1fr;
    text-align: center;
  }

  .hero-title {
    font-size: 2rem;
  }

  .hero-buttons {
    flex-direction: column;
    align-items: center;
  }

  .hero-stats {
    justify-content: center;
  }
}
</style>
