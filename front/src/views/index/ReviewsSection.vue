<template>
  <!-- 客户评价 -->
  <section id="reviews" class="reviews-section">
    <div class="container">
      <div class="section-header">
        <h2 class="section-title">客户评价</h2>
        <p class="section-description">听听客户对我们服务的真实评价</p>
      </div>

      <!-- 评价轮播容器 -->
      <div class="reviews-carousel">
        <!-- 左箭头 -->
        <button
          class="carousel-btn carousel-btn-prev"
          @click="prevReviewSlide"
          :disabled="!canGoPrevReview"
          :class="{ 'disabled': !canGoPrevReview }"
        >
          <ChevronLeft class="carousel-icon" />
        </button>

        <!-- 评价网格 -->
        <div class="reviews-grid-container">
          <div class="reviews-grid">
            <div
              v-for="review in displayedReviews"
              :key="review.id"
              class="review-card"
            >
              <div class="review-header">
                <div class="reviewer-avatar">
                  <span class="avatar-text">{{ review.author.charAt(0) }}</span>
                </div>
                <div class="reviewer-info">
                  <h4 class="reviewer-name">{{ review.author }}</h4>
                  <div class="review-stars">
                    <Star
                      v-for="i in 5"
                      :key="i"
                      class="star-icon"
                    />
                  </div>
                </div>
              </div>
              <p class="review-text">{{ review.text }}</p>
              <div class="review-service">
                <span class="service-tag">{{ review.service }}</span>
              </div>
            </div>
          </div>
        </div>

        <!-- 右箭头 -->
        <button
          class="carousel-btn carousel-btn-next"
          @click="nextReviewSlide"
          :disabled="!canGoNextReview"
          :class="{ 'disabled': !canGoNextReview }"
        >
          <ChevronRight class="carousel-icon" />
        </button>
      </div>

      <!-- 评价轮播指示器 -->
      <div class="carousel-indicators">
        <button
          v-for="(slide, index) in totalReviewSlides"
          :key="index"
          class="indicator-dot"
          :class="{ 'active': index === currentReviewSlide }"
          @click="goToReviewSlide(index)"
        ></button>
      </div>
    </div>
  </section>
</template>

<script setup>
import { ref, computed } from 'vue'
import {
  Star,
  ChevronLeft,
  ChevronRight
} from 'lucide-vue-next'

// 客户评价数据
const allReviews = ref([
  {
    id: 1,
    author: '张女士',
    text: '李阿姨服务非常专业，家里打扫得特别干净，而且人很细心负责，会继续选择这家的服务。',
    service: '家庭保洁'
  },
  {
    id: 2,
    author: '王先生',
    text: '月嫂张阿姨经验丰富，对宝宝和产妇都照顾得很好，让我们全家都很放心，强烈推荐！',
    service: '月嫂服务'
  },
  {
    id: 3,
    author: '李女士',
    text: '保姆王阿姨人很好，照顾老人很用心，家务也做得很好，我们全家都很满意她的服务。',
    service: '保姆服务'
  },
  {
    id: 4,
    author: '陈先生',
    text: '深度清洁服务真的很棒，连犄角旮旯都清理得干干净净，价格也很合理，下次还会选择。',
    service: '深度清洁'
  },
  {
    id: 5,
    author: '刘女士',
    text: '育儿嫂刘阿姨非常专业，对孩子很有耐心，还教了我很多育儿知识，非常感谢！',
    service: '育儿嫂'
  },
  {
    id: 6,
    author: '赵女士',
    text: '家庭保洁服务很满意，阿姨准时到达，工作认真负责，家里焕然一新。',
    service: '家庭保洁'
  }
])

// 评价轮播相关状态
const currentReviewSlide = ref(0)
const reviewsPerSlide = 3

// 计算显示的客户评价
const displayedReviews = computed(() => {
  const start = currentReviewSlide.value * reviewsPerSlide
  return allReviews.value.slice(start, start + reviewsPerSlide)
})

// 计算评价总页数
const totalReviewSlides = computed(() => {
  return Math.ceil(allReviews.value.length / reviewsPerSlide)
})

// 评价轮播控制方法
const nextReviewSlide = () => {
  if (currentReviewSlide.value < totalReviewSlides.value - 1) {
    currentReviewSlide.value++
  }
}

const prevReviewSlide = () => {
  if (currentReviewSlide.value > 0) {
    currentReviewSlide.value--
  }
}

// 跳转到指定评价页面
const goToReviewSlide = (index) => {
  currentReviewSlide.value = index
}

// 检查评价是否可以继续滑动
const canGoNextReview = computed(() => currentReviewSlide.value < totalReviewSlides.value - 1)
const canGoPrevReview = computed(() => currentReviewSlide.value > 0)
</script>

<style scoped lang="less">
.container {
  max-width: 1400px;
  margin: 0 auto;
  padding: 0 20px;
}

.section-header {
  text-align: center;
  margin-bottom: 1.5rem;
}

.section-title {
  font-size: 2.5rem;
  font-weight: bold;
  color: #1f2937;
  margin-bottom: 1rem;
}

.section-description {
  font-size: 1.125rem;
  color: #6b7280;
  max-width: 700px;
  margin: 0 auto;
  line-height: 1.6;
}

/* 客户评价 */
.reviews-section {
  padding: 5rem 0;
  background: #f9fafb;
}

/* 评价轮播容器 */
.reviews-carousel {
  display: flex;
  align-items: center;
  gap: 2rem;
  max-width: 1600px;
  margin: 0 auto;
  position: relative;
  padding: 0 1rem;
}

.reviews-grid-container {
  flex: 1;
  overflow: hidden;
  width: 100%;
}

.reviews-grid {
  display: grid;
  grid-template-columns: repeat(3, 1fr);
  gap: 2rem;
  width: 100%;
}

/* 轮播按钮 */
.carousel-btn {
  width: 3rem;
  height: 3rem;
  border-radius: 50%;
  border: none;
  background: white;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  transition: all 0.3s ease;
  flex-shrink: 0;
  z-index: 10;
}

.carousel-btn:hover:not(.disabled) {
  background: #3b82f6;
  transform: scale(1.1);
}

.carousel-btn:hover:not(.disabled) .carousel-icon {
  color: white;
}

.carousel-btn.disabled {
  opacity: 0.4;
  cursor: not-allowed;
  box-shadow: 0 2px 6px rgba(0, 0, 0, 0.1);
}

.carousel-icon {
  width: 1.5rem;
  height: 1.5rem;
  color: #6b7280;
}

/* 轮播指示器 */
.carousel-indicators {
  display: flex;
  justify-content: center;
  gap: 0.5rem;
  margin-top: 2rem;
}

.indicator-dot {
  width: 0.75rem;
  height: 0.75rem;
  border-radius: 50%;
  border: none;
  background: #d1d5db;
  cursor: pointer;
  transition: all 0.3s ease;
}

.indicator-dot.active {
  background: #3b82f6;
  transform: scale(1.2);
}

.indicator-dot:hover {
  background: #9ca3af;
}

.review-card {
  background: white;
  border-radius: 12px;
  padding: 2rem;
  box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
  transition: transform 0.3s ease;
  min-height: 280px;
  display: flex;
  flex-direction: column;
}

.review-card:hover {
  transform: translateY(-3px);
}

.review-header {
  display: flex;
  align-items: center;
  margin-bottom: 1.5rem;
}

.reviewer-avatar {
  width: 3rem;
  height: 3rem;
  background: linear-gradient(135deg, #3b82f6, #1d4ed8);
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-right: 1rem;
}

.avatar-text {
  color: white;
  font-size: 1.25rem;
  font-weight: bold;
}

.reviewer-info {
  flex: 1;
}

.reviewer-name {
  font-weight: 600;
  color: #1f2937;
  margin-bottom: 0.5rem;
}

.review-stars {
  display: flex;
  gap: 2px;
}

.star-icon {
  width: 1rem;
  height: 1rem;
  color: #fbbf24;
  fill: currentColor;
}

.review-text {
  color: #4b5563;
  line-height: 1.6;
  margin-bottom: 1rem;
  flex: 1;
}

.service-tag {
  background: #dbeafe;
  color: #1d4ed8;
  padding: 0.25rem 0.75rem;
  border-radius: 20px;
  font-size: 0.75rem;
  font-weight: 500;
}

/* 响应式设计 */
@media (max-width: 1200px) {
  .reviews-grid {
    grid-template-columns: repeat(2, 1fr);
  }

  .reviews-carousel {
    gap: 1rem;
    max-width: 1000px;
  }
}

@media (max-width: 768px) {
  .section-title {
    font-size: 2rem;
  }

  .reviews-grid {
    grid-template-columns: 1fr;
  }

  .reviews-carousel {
    flex-direction: column;
    gap: 1.5rem;
    max-width: 400px;
  }

  .reviews-grid-container {
    order: 1;
  }

  .carousel-btn {
    width: 2rem;
    height: 2rem;
    position: static;
  }

  .carousel-icon {
    width: 1rem;
    height: 1rem;
  }

  .carousel-btn-prev,
  .carousel-btn-next {
    order: 2;
  }

  .carousel-indicators {
    order: 3;
    margin-top: 1rem;
  }
}
</style>
