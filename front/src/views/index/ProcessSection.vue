<template>
  <!-- 服务流程 -->
  <section id="process" class="process-section">
    <div class="container">
      <div class="section-header">
        <h2 class="section-title">服务流程</h2>
        <p class="section-description">简单四步，享受专业家政服务</p>
      </div>
      <div class="process-steps">
        <div
          v-for="(step, index) in processSteps"
          :key="step.id"
          class="process-step"
        >
          <div class="step-number">{{ index + 1 }}</div>
          <div class="step-icon">
            <component :is="step.icon" class="icon" />
          </div>
          <h3 class="step-title">{{ step.title }}</h3>
          <p class="step-description">{{ step.description }}</p>
        </div>
      </div>
    </div>
  </section>
</template>

<script setup>
import { ref } from 'vue'
import {
  Phone,
  Search,
  UserCheck,
  Award
} from 'lucide-vue-next'

// 服务流程数据
const processSteps = ref([
  {
    id: 1,
    title: '在线咨询',
    description: '通过电话或在线平台咨询服务详情',
    icon: Phone
  },
  {
    id: 2,
    title: '需求匹配',
    description: '根据您的需求匹配合适的服务人员',
    icon: Search
  },
  {
    id: 3,
    title: '上门服务',
    description: '专业人员按约定时间上门提供服务',
    icon: UserCheck
  },
  {
    id: 4,
    title: '服务反馈',
    description: '服务完成后进行质量评价和反馈',
    icon: Award
  }
])
</script>

<style scoped lang="less">
.container {
  max-width: 1400px;
  margin: 0 auto;
  padding: 0 20px;
}

.section-header {
  text-align: center;
  margin-bottom: 1.5rem;
}

.section-title {
  font-size: 2.5rem;
  font-weight: bold;
  color: #1f2937;
  margin-bottom: 1rem;
}

.section-description {
  font-size: 1.125rem;
  color: #6b7280;
  max-width: 700px;
  margin: 0 auto;
  line-height: 1.6;
}

/* 服务流程 */
.process-section {
  padding: 5rem 0;
  background: #f9fafb;
}

.process-steps {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
  gap: 2rem;
}

.process-step {
  text-align: center;
  position: relative;
}

.step-number {
  position: absolute;
  top: -1rem;
  left: 50%;
  transform: translateX(-50%);
  width: 2rem;
  height: 2rem;
  background: #3b82f6;
  color: white;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  font-weight: bold;
  font-size: 0.875rem;
}

.step-icon {
  width: 4rem;
  height: 4rem;
  background: white;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  margin: 0 auto 1.5rem;
  box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
}

.step-icon .icon {
  width: 2rem;
  height: 2rem;
  color: #3b82f6;
}

.step-title {
  font-size: 1.25rem;
  font-weight: 600;
  color: #1f2937;
  margin-bottom: 0.75rem;
}

.step-description {
  color: #6b7280;
}
</style>
