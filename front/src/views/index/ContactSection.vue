<template>
  <!-- 联系我们 -->
  <section id="contact" class="contact-section">
    <div class="container">
      <div class="contact-content">
        <div class="contact-info">
          <h2 class="contact-title">联系我们</h2>
          <p class="contact-description">随时为您提供专业的家政服务咨询</p>
          <div class="contact-items">
            <div class="contact-item">
              <Phone class="contact-icon" />
              <div class="contact-details">
                <h4>服务热线</h4>
                <p>************</p>
              </div>
            </div>
            <div class="contact-item">
              <MapPin class="contact-icon" />
              <div class="contact-details">
                <h4>服务区域</h4>
                <p>全市各区域均可服务</p>
              </div>
            </div>
            <div class="contact-item">
              <Clock class="contact-icon" />
              <div class="contact-details">
                <h4>服务时间</h4>
                <p>7:00 - 22:00 全年无休</p>
              </div>
            </div>
          </div>
        </div>
        <div class="contact-form">
          <h3 class="form-title">在线预约</h3>
          <form class="booking-form">
            <div class="form-group">
              <input type="text" placeholder="您的姓名" class="form-input" />
            </div>
            <div class="form-group">
              <input type="tel" placeholder="联系电话" class="form-input" />
            </div>
            <div class="form-group">
              <select class="form-select">
                <option>选择服务类型</option>
                <option>家庭保洁</option>
                <option>深度清洁</option>
                <option>保姆服务</option>
                <option>月嫂服务</option>
              </select>
            </div>
            <div class="form-group">
              <textarea placeholder="服务需求描述" class="form-textarea"></textarea>
            </div>
            <button type="submit" class="form-submit">提交预约</button>
          </form>
        </div>
      </div>
    </div>
  </section>
</template>

<script setup>
import { Phone, MapPin, Clock } from 'lucide-vue-next'
</script>

<style scoped lang="less">
.container {
  max-width: 1400px;
  margin: 0 auto;
  padding: 0 20px;
}

/* 联系我们 */
.contact-section {
  padding: 5rem 0;
  background: white;
}

.contact-content {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 4rem;
  align-items: start;
}

.contact-title {
  font-size: 2.5rem;
  font-weight: bold;
  color: #1f2937;
  margin-bottom: 1rem;
}

.contact-description {
  color: #6b7280;
  font-size: 1.125rem;
  margin-bottom: 2rem;
}

.contact-items {
  display: flex;
  flex-direction: column;
  gap: 1.5rem;
}

.contact-item {
  display: flex;
  align-items: center;
  gap: 1rem;
}

.contact-icon {
  width: 2.5rem;
  height: 2.5rem;
  color: #3b82f6;
  background: #dbeafe;
  padding: 0.5rem;
  border-radius: 8px;
}

.contact-details h4 {
  font-weight: 600;
  color: #1f2937;
  margin-bottom: 0.25rem;
}

.contact-details p {
  color: #6b7280;
}

.form-title {
  font-size: 1.5rem;
  font-weight: 600;
  color: #1f2937;
  margin-bottom: 1.5rem;
}

.booking-form {
  display: flex;
  flex-direction: column;
  gap: 1rem;
}

.form-group {
  display: flex;
  flex-direction: column;
}

.form-input,
.form-select,
.form-textarea {
  padding: 0.875rem;
  border: 1px solid #d1d5db;
  border-radius: 8px;
  font-size: 1rem;
  transition: border-color 0.3s ease;
}

.form-input:focus,
.form-select:focus,
.form-textarea:focus {
  outline: none;
  border-color: #3b82f6;
}

.form-textarea {
  min-height: 100px;
  resize: vertical;
}

.form-submit {
  background: #3b82f6;
  color: white;
  border: none;
  padding: 1rem;
  border-radius: 8px;
  font-size: 1.125rem;
  font-weight: 500;
  cursor: pointer;
  transition: background-color 0.3s ease;
}

.form-submit:hover {
  background: #2563eb;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .contact-content {
    grid-template-columns: 1fr;
  }

  .contact-title {
    font-size: 2rem;
  }
}
</style>
