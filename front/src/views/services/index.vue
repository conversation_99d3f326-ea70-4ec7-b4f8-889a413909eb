<template>
  <div class="home-services">
    <!-- 全屏加载动画 -->
    <div v-if="loading" class="loading-overlay">
      <div class="loading-container">
        <div class="loading-spinner">
          <div class="spinner-ring"></div>
          <div class="spinner-ring"></div>
          <div class="spinner-ring"></div>
        </div>
        <div class="loading-text">{{ loadingText }}</div>
        <div class="loading-dots">
          <span class="dot"></span>
          <span class="dot"></span>
          <span class="dot"></span>
        </div>
      </div>
    </div>
    <!-- Header Section -->
    <header class="hero-section">
      <div class="hero-content">
        <h1 class="hero-title">专业家政服务，满足您的一切需求</h1>
        <p class="hero-subtitle">我们提供高质量、可靠的家政服务，让您的生活更加轻松舒适</p>

        <!-- Search Bar -->
        <div class="search-container">
          <input
            type="text"
            v-model="searchQuery"
            placeholder="搜索您需要的服务..."
            class="search-input"
          />
          <button class="search-button" @click="handleSearch">
            <svg width="20" height="20" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
              <circle cx="11" cy="11" r="8"></circle>
              <path d="m21 21-4.35-4.35"></path>
            </svg>
          </button>
        </div>
      </div>
    </header>

    <!-- Navigation Tabs -->
    <nav class="nav-tabs">
      <div class="nav-container">
        <button
          v-for="tab in navTabs"
          :key="tab.id"
          :class="['nav-tab', { active: activeTab === tab.name }]"
          @click="setActiveTab(tab.name)"
        >
          <t-icon v-if="tab.icon" :name="tab.icon" class="nav-icon" />
          {{ tab.name }}
        </button>
      </div>
    </nav>

    <!-- Services Section -->
    <section class="services-section">
      <div class="services-container">
        <div class="services-header">
          <div class="services-title-section">
            <h2 class="services-title">我们的服务</h2>
            <p class="services-subtitle">专业的家政服务团队，为您提供全方位的家庭清洁解决方案，让您的家焕然一新</p>
          </div>
        </div>

        <!-- Services Grid -->
        <div class="services-grid">
          <div
            v-for="service in services"
            :key="service.serveId"
            class="service-card"
            @click="handleClick(service.serveId)"
          >
            <div class="service-image">
              <img :src="service.image" :alt="service.name" />
              <span :class="['service-badge', service.category ? service.category.toLowerCase() : 'default']">{{ service.category || '服务' }}</span>
            </div>

            <div class="service-content">
              <h3 class="service-name">{{ service.name }}</h3>
              <p class="service-description">{{ service.description }}</p>

              <div class="service-tags">
                <span v-for="tag in service.tags" :key="tag" class="service-tag">
                  {{ tag }}
                </span>
              </div>

              <div class="service-price">
                <span class="price-text">¥{{ service.price }} 起/次</span>
              </div>
            </div>
          </div>
        </div>

        <!-- 分页组件 -->
        <div class="pagination-container">
          <t-pagination
            v-model="currentPage"
            :total="totalServices"
            :page-size="pageSize"
            :show-sizer="true"
            :show-jumper="true"
            :page-size-options="pageSizeOptions"
            @change="onPageChange"
          />
        </div>
      </div>
    </section>
  </div>
</template>

<script setup>
import { ref, reactive, computed, onMounted } from 'vue'
import { ListServe } from '@/api/serve.js'
import { MessagePlugin } from 'tdesign-vue-next';
import { useRouter } from 'vue-router'

const router = useRouter()

onMounted(async () => {
  await listServe()
})

const handleClick = (serveId) => {
  router.push(`/service/${serveId}`)
}

const listServe = async (loadingMessage = '正在加载服务数据...') => {
  loading.value = true
  loadingText.value = loadingMessage
  try {
    const response = await ListServe({
      pageNum: currentPage.value,
      pageSize: pageSize.value,
      name: searchQuery.value === '' ? null : searchQuery.value,
      tag: activeTab.value === '全部服务' ? null : activeTab.value,
      isAsc: "ascending"

    })
    if(response.code !== 200){
      await MessagePlugin.error(response.msg || "获取服务列表失败")
      return
    }

  // 处理后端返回的数据，解析 tag 字段
  const processedServices = (response.rows || []).map(service => {
    let tags = []
    if (service.tag) {
      try {
        // 如果 tag 是字符串格式的 JSON 数组，解析它
        if (typeof service.tag === 'string') {
          tags = JSON.parse(service.tag)
        } else if (Array.isArray(service.tag)) {
          tags = service.tag
        }
      } catch (error) {
        console.warn('解析服务标签失败:', error)
        // 如果解析失败，尝试按逗号分割
        tags = service.tag.split(',').map(tag => tag.trim()).filter(tag => tag)
      }
    }
    return {
      ...service,
      tags: tags,
      // 确保字段名称一致
      serveId: service.serveId || service.id,
      name: service.name || service.title,
      image: service.image || service.serviceImage,
      description: service.description,
      price: service.price,
      category: service.category || service.badge
    }
  })

    services.value = processedServices
    totalServices.value = response.total || 0
  } catch (error) {
    console.error('获取服务列表失败:', error)
    await MessagePlugin.error('获取服务列表失败，请稍后重试')
  } finally {
    loading.value = false
  }
}

const searchQuery = ref('')
const activeTab = ref('全部服务')
const loading = ref(false)
const loadingText = ref('正在加载服务数据...')

// 分页相关的响应式数据
const currentPage = ref(1)
const pageSize = ref(8) // 默认每页显示8个服务
const pageSizeOptions = ref([4, 8, 12, 16, 20])
const totalServices = ref(0)

// 标签
const navTabs = [
  { id: 'all', name: '全部服务', icon: 'view-list' },
  { id: 'daily', name: '日常保洁', icon: 'clear' },
  { id: 'deep', name: '深度清洁', icon: 'refresh' },
  { id: 'clothes', name: '衣物洗护', icon: 'accessibility' },
  { id: 'kitchen', name: '厨房清洁', icon: 'fork' },
  { id: 'appliance', name: '家电清洗', icon: 'tv' },
  { id: 'newhouse', name: '新居开荒', icon: 'brush' },
  { id: 'aircon', name: '空调清洗', icon: 'precise-monitor' }
]

// 服务列表
const services = ref([])

const setActiveTab = async (tabName) => {
  activeTab.value = tabName
  // 切换标签时重置到第一页
  currentPage.value = 1
  // 重新获取数据
  await listServe(`正在加载${tabName}...`)
}

const handleSearch = async () => {
  console.log('搜索:', searchQuery.value)
  // 搜索时重置到第一页
  currentPage.value = 1
  // 重新获取数据
  await listServe('正在搜索服务...')
}

// 分页事件处理函数
const onPageChange = async (pageInfo) => {
  console.log('page-info:', pageInfo)
  currentPage.value = pageInfo.current
  pageSize.value = pageInfo.pageSize

  // 重新获取数据
  await listServe('正在加载数据...')

  // 滚动到服务区域顶部
  const servicesSection = document.querySelector('.services-section')
  if (servicesSection) {
    servicesSection.scrollIntoView({
      behavior: 'smooth',
      block: 'start'
    })
  }
}
</script>

<style scoped>

* {
  margin: 0;
  padding: 0;
  box-sizing: border-box;
}

/* 全屏加载动画 */
.loading-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: linear-gradient(135deg, rgba(37, 99, 235, 0.1) 0%, rgba(79, 70, 229, 0.1) 100%);
  backdrop-filter: blur(10px);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 9999;
  animation: overlayFadeIn 0.4s ease-out;
}

.loading-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 24px;
  animation: containerSlideIn 0.6s ease-out;
}

/* 自定义旋转加载器 */
.loading-spinner {
  position: relative;
  width: 80px;
  height: 80px;
}

.spinner-ring {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  border: 3px solid transparent;
  border-radius: 50%;
  animation: spin 2s linear infinite;
}

.spinner-ring:nth-child(1) {
  border-top-color: #2563eb;
  animation-duration: 2s;
}

.spinner-ring:nth-child(2) {
  border-right-color: #3b82f6;
  animation-duration: 1.5s;
  animation-direction: reverse;
  width: 90%;
  height: 90%;
  top: 5%;
  left: 5%;
}

.spinner-ring:nth-child(3) {
  border-bottom-color: #60a5fa;
  animation-duration: 1s;
  width: 80%;
  height: 80%;
  top: 10%;
  left: 10%;
}

/* 加载文字 */
.loading-text {
  font-size: 18px;
  font-weight: 600;
  color: #1f2937;
  text-align: center;
  letter-spacing: 0.5px;
  margin: 0;
  text-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

/* 跳动的点 */
.loading-dots {
  display: flex;
  gap: 8px;
  align-items: center;
}

.dot {
  width: 8px;
  height: 8px;
  background: #2563eb;
  border-radius: 50%;
  animation: dotBounce 1.4s ease-in-out infinite both;
}

.dot:nth-child(1) {
  animation-delay: -0.32s;
}

.dot:nth-child(2) {
  animation-delay: -0.16s;
}

.dot:nth-child(3) {
  animation-delay: 0s;
}

/* 动画定义 */
@keyframes overlayFadeIn {
  from {
    opacity: 0;
  }
  to {
    opacity: 1;
  }
}

@keyframes containerSlideIn {
  from {
    opacity: 0;
    transform: translateY(30px) scale(0.9);
  }
  to {
    opacity: 1;
    transform: translateY(0) scale(1);
  }
}

@keyframes spin {
  from {
    transform: rotate(0deg);
  }
  to {
    transform: rotate(360deg);
  }
}

@keyframes dotBounce {
  0%, 80%, 100% {
    transform: scale(0.8);
    opacity: 0.5;
  }
  40% {
    transform: scale(1.2);
    opacity: 1;
  }
}

/* 响应式设计 */
@media (max-width: 768px) {
  .loading-spinner {
    width: 60px;
    height: 60px;
  }

  .loading-text {
    font-size: 16px;
  }

  .dot {
    width: 6px;
    height: 6px;
  }
}

.home-services {
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
  line-height: 1.6;
  color: #333;
}

/* Hero Section */
.hero-section {
  background: linear-gradient(135deg, #2563eb 0%, #4f46e5 100%);
  padding: 80px 20px;
  text-align: center;
  color: white;
}

.hero-content {
  max-width: 800px;
  margin: 0 auto;
}

.hero-title {
  font-size: 2.5rem;
  font-weight: 700;
  margin-bottom: 16px;
  letter-spacing: -0.02em;
}

.hero-subtitle {
  font-size: 1.1rem;
  opacity: 0.9;
  margin-bottom: 40px;
  font-weight: 400;
  color: #bfdbfe;
}

.search-container {
  display: flex;
  max-width: 500px;
  margin: 0 auto;
  background: white;
  border-radius: 50px;
  overflow: hidden;
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
}

.search-input {
  flex: 1;
  padding: 16px 24px;
  border: none;
  outline: none;
  font-size: 1rem;
  color: #333;
}

.search-input::placeholder {
  color: #999;
}

.search-button {
  background: #f97316;
  border: none;
  padding: 16px 20px;
  color: white;
  cursor: pointer;
  transition: background-color 0.2s;
}

.search-button:hover {
  background: #ea580c;
}

/* Navigation Tabs */
.nav-tabs {
  background: white;
  border-bottom: 1px solid #e5e7eb;
  padding: 0 20px;
  position: sticky;
  top: 0;
  z-index: 40;
  box-shadow: 0 1px 3px 0 rgba(0, 0, 0, 0.1);
}

.nav-container {
  max-width: 1200px;
  margin: 0 auto;
  display: flex;
  gap: 8px;
  overflow-x: auto;
  padding: 16px 0;
}

.nav-tab {
  display: flex;
  align-items: center;
  gap: 8px;
  padding: 12px 20px;
  border: none;
  background: #f3f4f6;
  color: #4b5563;
  cursor: pointer;
  white-space: nowrap;
  border-radius: 50px;
  transition: all 0.2s;
  font-size: 0.95rem;
  font-weight: 500;
}

.nav-tab:hover {
  color: #374151;
  background: #e5e7eb;
}

.nav-tab.active {
  background: #2563eb;
  color: white;
}

.nav-icon {
  font-size: 1rem;
}

/* Services Section */
.services-section {
  padding: 60px 20px;
  background: #f9fafb;
}

.services-container {
  max-width: 1400px;
  margin: 0 auto;
}

.services-header {
  margin-bottom: 40px;
}

.services-title-section {
  text-align: left;
}

.services-title {
  font-size: 2rem;
  font-weight: 700;
  color: #1f2937;
  margin-bottom: 8px;
}

.services-subtitle {
  color: #6b7280;
  font-size: 1rem;
}

/* Services Grid */
.services-grid {
  display: grid;
  grid-template-columns: repeat(4, 1fr);
  gap: 20px;
}

.service-card {
  background: white;
  border-radius: 12px;
  overflow: hidden;
  box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1);
  transition: all 0.3s;
  cursor: pointer;
}

.service-card:hover {
  transform: translateY(-4px);
  box-shadow: 0 10px 25px -3px rgba(0, 0, 0, 0.1);
}

.service-image {
  position: relative;
  height: 200px;
  overflow: hidden;
}

.service-image img {
  width: 100%;
  height: 100%;
  object-fit: cover;
}

.service-badge {
  position: absolute;
  top: 12px;
  right: 12px;
  padding: 6px 12px;
  border-radius: 20px;
  font-size: 0.8rem;
  font-weight: 600;
  color: white;
  background: #2563eb;
}

.service-content {
  padding: 20px;
}

.service-name {
  font-size: 1.25rem;
  font-weight: 600;
  color: #1f2937;
  margin-bottom: 12px;
}

.service-description {
  color: #6b7280;
  font-size: 0.9rem;
  margin-bottom: 16px;
  line-height: 1.6;
  display: -webkit-box;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
  overflow: hidden;
  text-overflow: ellipsis;
}

.service-tags {
  display: flex;
  flex-wrap: wrap;
  gap: 8px;
  margin-bottom: 16px;
}

.service-tag {
  padding: 6px 12px;
  background: #f3f4f6;
  color: #6b7280;
  border-radius: 20px;
  font-size: 0.8rem;
  font-weight: 500;
}

.service-price {
  text-align: left;
}

.price-text {
  font-size: 1.1rem;
  font-weight: 600;
  color: #ff4f24;
}

/* 分页样式 */
.pagination-container {
  display: flex;
  justify-content: center;
  align-items: center;
  padding: 40px 0 20px;
  width: 100%;
}

/* Responsive Design */
@media (max-width: 1200px) {
  .services-grid {
    grid-template-columns: repeat(3, 1fr);
    gap: 24px;
  }
}

@media (max-width: 900px) {
  .services-grid {
    grid-template-columns: repeat(2, 1fr);
    gap: 24px;
  }
}

@media (max-width: 768px) {
  .hero-title {
    font-size: 2rem;
  }

  .services-grid {
    grid-template-columns: 1fr;
  }

  .nav-container {
    padding: 0 10px;
  }

  .service-card {
    margin: 0 auto;
    max-width: 350px;
  }

  .pagination-container {
    padding: 30px 0 15px;
  }

  .pagination-container :deep(.t-pagination) {
    font-size: 14px;
  }
}
</style>
