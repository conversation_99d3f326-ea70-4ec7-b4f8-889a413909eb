<template>
  <div class="booking-card">
    <!-- 预约状态标签 -->
    <div class="booking-status">
      <span :class="['status-badge', getStatusClass(booking.status)]">
        {{ getStatusText(booking.status) }}
      </span>
      <span class="booking-id">预约编号：{{ booking.reservationId }}</span>
    </div>

    <!-- 预约内容 -->
    <div class="booking-content">
      <!-- 服务信息 -->
      <div class="service-info">
        <div class="service-image">
          <img :src="booking.serve?.image" :alt="booking.serve?.name" />
        </div>
        <div class="service-details">
          <h3 class="service-name">{{ booking.serve?.name }}</h3>
          <p class="service-description">{{ booking.serve?.description }}</p>
          <div class="service-tags">
            <span
              v-for="tag in booking.serviceTags"
              :key="tag"
              class="service-tag"
            >
              {{ tag }}
            </span>
          </div>
        </div>
      </div>

      <!-- 预约信息 -->
      <div class="booking-info">
        <div class="info-row">
          <div class="info-item">
            <t-icon name="user" class="info-icon" />
            <span class="info-label">联系人：</span>
            <span class="info-value">{{ booking.contactName }}</span>
          </div>
          <div class="info-item">
            <t-icon name="call" class="info-icon" />
            <span class="info-label">联系电话：</span>
            <span class="info-value">{{ booking.phone }}</span>
          </div>
        </div>
        <div class="info-row">
          <div class="info-item">
            <t-icon name="location" class="info-icon" />
            <span class="info-label">服务地址：</span>
            <span class="info-value">{{ booking.address }}</span>
          </div>
        </div>
        <div class="info-row">
          <div class="info-item">
            <t-icon name="time" class="info-icon" />
            <span class="info-label">服务时间：</span>
            <span class="info-value">{{ formatDateTime(booking.serviceDate, booking.serviceTime) }}</span>
          </div>
          <div class="info-item">
            <t-icon name="money" class="info-icon" />
            <span class="info-label">服务费用：</span>
            <span class="info-value price">¥{{ booking.serve?.price }}</span>
          </div>
        </div>
        <div v-if="booking.special" class="info-row">
          <div class="info-item full-width">
            <t-icon name="edit" class="info-icon" />
            <span class="info-label">特殊要求：</span>
            <span class="info-value">{{ booking.special }}</span>
          </div>
        </div>
        <!-- 取消原因显示 -->
        <div v-if="booking.status === '已取消' && booking.cancelReason" class="cancel-notice">
          <div class="cancel-alert">
            <div class="cancel-alert-header">
              <t-icon name="info-circle" class="cancel-alert-icon" />
              <span class="cancel-alert-title">当前预约已取消</span>
            </div>
            <div class="cancel-reason-content">
              <span class="cancel-reason-label">取消原因：</span>
              <span class="cancel-alert-body">{{ booking.cancelReason }}</span>
            </div>
          </div>
        </div>
      </div>

      <!-- 服务人员信息分隔区域 -->
      <div class="staff-section">
        <div class="section-divider">
          <div class="divider-line"></div>
          <span class="divider-text">服务人员信息</span>
          <div class="divider-line"></div>
        </div>

        <div v-if="hasAssignedWorker(booking)" class="staff-info">
          <div class="staff-avatar">
            <img v-if="booking.staff?.image" :src="booking.staff.image" :alt="booking.staff.name" />
            <div v-else class="default-avatar">
              <t-icon name="user" />
            </div>
          </div>
          <div class="staff-details">
            <div class="staff-basic">
              <h4 class="staff-name">{{ booking.staff.name }}</h4>
              <span class="staff-title">{{ booking.staff.title || '服务人员' }}</span>
              <div class="staff-rating" v-if="booking.staff.star">
                <t-rate
                  :value="booking.staff.star"
                  :count="5"
                  size="small"
                  readonly
                  allow-half
                />
                <span class="rating-value">{{ booking.staff.star }}</span>
              </div>
            </div>
            <div class="staff-contact">
              <div class="contact-item" v-if="booking.staff.phone">
                <t-icon name="call" class="contact-icon" />
                <span class="contact-label">联系电话：</span>
                <span class="contact-text">{{ booking.staff.phone }}</span>
              </div>
              <div class="contact-item" v-if="booking.staff.experience">
                <t-icon name="time" class="contact-icon" />
                <span class="contact-label">工作经验：</span>
                <span class="contact-text">{{ booking.staff.experience }}</span>
              </div>
            </div>
            <div class="staff-tags" v-if="booking.staff.tag">
              <span
                v-for="tag in getStaffTags(booking.staff.tag)"
                :key="tag"
                class="staff-tag"
              >
                {{ tag }}
              </span>
            </div>
          </div>
        </div>

        <div v-else class="no-staff">
          <div class="no-staff-icon">
            <t-icon name="user-circle" />
          </div>
          <div class="no-staff-text">
            <p class="no-staff-title">暂未分配服务人员</p>
            <p class="no-staff-desc">我们会尽快为您安排合适的服务人员</p>
          </div>
        </div>
      </div>

      <!-- 操作按钮 -->
      <div class="booking-actions">
        <t-button
          v-if="canPay(booking.status)"
          theme="primary"
          size="small"
          @click="$emit('pay', booking)"
        >
          立即支付
        </t-button>
        <t-button
          v-if="canCancel(booking.status)"
          theme="default"
          size="small"
          @click="$emit('cancel', booking)"
        >
          取消预约
        </t-button>
        <t-button
          v-if="canModify(booking.status)"
          theme="default"
          size="small"
          @click="$emit('modify', booking)"
        >
          修改预约
        </t-button>
        <t-button
          v-if="canRate(booking.status)"
          theme="primary"
          size="small"
          @click="$emit('rate', booking)"
        >
          评价服务
        </t-button>
      </div>
    </div>
  </div>
</template>

<script setup>
// Props
defineProps({
  booking: {
    type: Object,
    required: true
  }
})

// Emits
defineEmits(['pay', 'cancel', 'modify', 'rate'])

// 状态相关方法
const getStatusClass = (status) => {
  const statusMap = {
    '待支付': 'status-pending',
    '已支付': 'status-confirmed',
    '已取消': 'status-cancelled',
    '待服务': 'status-waiting',
    '服务中': 'status-progress',
    '已完成': 'status-completed'
  }
  return statusMap[status] || 'status-default'
}

const getStatusText = (status) => {
  // 直接返回状态值，因为后端返回的就是中文状态
  return status || '未知状态'
}

// 时间格式化
const formatDateTime = (date, timeSlot) => {
  // 直接返回日期和时间，因为后端返回的时间格式已经是具体时间段
  return `${date} ${timeSlot}`
}

// 检查是否有分配服务人员
const hasAssignedWorker = (booking) => {
  return booking.staff && booking.staff.name
}

// 获取服务人员信息
const getWorkerInfo = (booking) => {
  if (!booking.staff || !booking.staff.name) {
    return '暂未分配服务人员'
  }

  const staff = booking.staff
  const name = staff.name
  const title = staff.title || '服务人员'
  const phone = staff.phone

  if (phone) {
    return `${name}（${title}，${phone}）`
  }
  return `${name}（${title}）`
}

// 解析服务人员标签
const getStaffTags = (tagString) => {
  if (!tagString) return []

  try {
    // 如果是JSON格式的字符串，解析它
    if (typeof tagString === 'string' && tagString.startsWith('[')) {
      return JSON.parse(tagString)
    }
    // 如果是普通字符串，按逗号分割
    return tagString.split(',').map(tag => tag.trim()).filter(tag => tag)
  } catch (error) {
    console.warn('解析服务人员标签失败:', error)
    return []
  }
}

// 操作权限判断
const canCancel = (status) => {
  return ['待支付', '已支付', '待服务'].includes(status)
}

const canModify = (status) => {
  return ['待支付', '已支付'].includes(status)
}

const canRate = (status) => {
  return status === '已完成'
}

const canPay = (status) => {
  return status === '待支付'
}
</script>

<style lang="less" scoped>
// 颜色变量
@primary: #2563eb;
@primary-light: #4f46e5;
@success: #00a870;
@warning: #ed7b2f;
@error: #d54941;
@dark: #1f2937;
@muted: #6b7280;
@border: #e5e7eb;
@price-color: #ff4f24;

.booking-card {
  background: white;
  border-radius: 12px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.06);
  border: 1px solid @border;
  margin-bottom: 1.5rem;
  transition: all 0.3s ease;

  &:last-child {
    margin-bottom: 0;
  }

  &:hover {
    box-shadow: 0 4px 16px rgba(0, 0, 0, 0.12);
    transform: translateY(-2px);
    border-color: rgba(22, 93, 255, 0.2);
  }
}

.booking-status {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 1rem 1.5rem 0.5rem;
}

.status-badge {
  padding: 0.25rem 0.75rem;
  border-radius: 12px;
  font-size: 0.75rem;
  font-weight: 600;

  &.status-pending {
    background: rgba(237, 123, 47, 0.1);
    color: @warning;
  }

  &.status-confirmed {
    background: rgba(0, 168, 112, 0.1);
    color: @success;
  }

  &.status-waiting {
    background: rgba(22, 93, 255, 0.1);
    color: @primary;
  }

  &.status-progress {
    background: rgba(79, 70, 229, 0.1);
    color: @primary-light;
  }

  &.status-completed {
    background: rgba(0, 168, 112, 0.1);
    color: @success;
  }

  &.status-cancelled {
    background: rgba(213, 73, 65, 0.1);
    color: @error;
  }
}

.booking-id {
  color: @muted;
  font-size: 0.875rem;
}

.booking-content {
  padding: 0 1.5rem 1.5rem;
}

// 服务信息
.service-info {
  display: flex;
  gap: 1rem;
  margin-bottom: 1.5rem;
}

.service-image {
  width: 80px;
  height: 80px;
  border-radius: 8px;
  overflow: hidden;
  flex-shrink: 0;

  img {
    width: 100%;
    height: 100%;
    object-fit: cover;
  }
}

.service-details {
  flex: 1;
}

.service-name {
  font-size: 1.125rem;
  font-weight: 600;
  color: @dark;
  margin: 0 0 0.5rem 0;
}

.service-description {
  color: @muted;
  font-size: 0.875rem;
  margin: 0 0 0.75rem 0;
  line-height: 1.5;
}

.service-tags {
  display: flex;
  flex-wrap: wrap;
  gap: 0.5rem;
}

.service-tag {
  padding: 0.25rem 0.5rem;
  background: rgba(22, 93, 255, 0.1);
  color: @primary;
  font-size: 0.75rem;
  border-radius: 4px;
}

// 预约信息
.booking-info {
  margin-bottom: 1.5rem;
}

.info-row {
  display: grid;
  grid-template-columns: 1fr;
  gap: 0.75rem;
  margin-bottom: 0.75rem;

  @media (min-width: 768px) {
    grid-template-columns: 1fr 1fr;
  }

  &:last-child {
    margin-bottom: 0;
  }
}

.info-item {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  font-size: 0.875rem;

  &.full-width {
    grid-column: 1 / -1;
  }


}

.info-icon {
  color: @primary;
  flex-shrink: 0;
}

.info-label {
  color: @muted;
  font-weight: 500;
  flex-shrink: 0;
}

.info-value {
  color: @dark;
  flex: 1;

  &.price {
    color: @price-color;
    font-weight: 600;
    font-size: 1rem;
  }

  &.unassigned {
    color: @muted;
    font-style: italic;
  }


}

// 取消通知样式
.cancel-notice {
  margin: 1.5rem 0;
}

.cancel-alert {
  background: #fef7f0;
  border-radius: 12px;
  padding: 1.25rem;
  box-shadow: 0 2px 8px rgba(237, 123, 47, 0.1);
  border: 1px solid #fed7aa;
}

.cancel-alert-header {
  display: flex;
  align-items: center;
  gap: 0.75rem;
  margin-bottom: 1rem;
}

.cancel-alert-icon {
  color: @warning;
  font-size: 1.125rem;
  flex-shrink: 0;
}

.cancel-alert-title {
  color: @warning;
  font-size: 1rem;
  font-weight: 600;
  letter-spacing: 0.025em;
}

.cancel-reason-content {
  margin-left: 1.875rem;
  margin-top: 0.75rem;
}

.cancel-reason-label {
  color: #92400e;
  font-size: 0.875rem;
  font-weight: 500;
  display: inline;
}

.cancel-alert-body {
  color: #92400e;
  font-size: 0.875rem;
  line-height: 1.6;
  word-break: break-word;
  display: inline;
}

// 服务人员信息区域
.staff-section {
  margin-top: 1.5rem;
  padding-top: 1rem;
}

.section-divider {
  display: flex;
  align-items: center;
  margin-bottom: 1rem;
  gap: 1rem;
}

.divider-line {
  flex: 1;
  height: 1px;
  background: @border;
}

.divider-text {
  color: @muted;
  font-size: 0.875rem;
  font-weight: 500;
  white-space: nowrap;
}

.staff-info {
  display: flex;
  gap: 1rem;
  padding: 1rem;
  background: rgba(22, 93, 255, 0.02);
  border-radius: 8px;
  border: 1px solid rgba(22, 93, 255, 0.1);
}

.staff-avatar {
  width: 60px;
  height: 60px;
  border-radius: 50%;
  overflow: hidden;
  flex-shrink: 0;
  border: 2px solid rgba(22, 93, 255, 0.1);

  img {
    width: 100%;
    height: 100%;
    object-fit: cover;
  }
}

.default-avatar {
  width: 100%;
  height: 100%;
  background: rgba(22, 93, 255, 0.1);
  display: flex;
  align-items: center;
  justify-content: center;
  color: @primary;
  font-size: 1.5rem;
}

.staff-details {
  flex: 1;
  display: flex;
  flex-direction: column;
  gap: 0.75rem;
}

.staff-basic {
  display: flex;
  align-items: center;
  gap: 0.75rem;
  flex-wrap: wrap;
}

.staff-name {
  font-size: 1rem;
  font-weight: 600;
  color: @dark;
  margin: 0;
}

.staff-title {
  padding: 0.25rem 0.5rem;
  background: rgba(22, 93, 255, 0.1);
  color: @primary;
  font-size: 0.75rem;
  border-radius: 4px;
  font-weight: 500;
}

.staff-rating {
  display: flex;
  align-items: center;
  gap: 0.5rem;
}

.rating-value {
  color: @dark;
  font-size: 0.875rem;
  font-weight: 500;
}

.staff-contact {
  display: flex;
  flex-direction: column;
  gap: 0.5rem;
}

.contact-item {
  display: flex;
  align-items: center;
  gap: 0.5rem;
}

.contact-icon {
  color: @primary;
  font-size: 0.875rem;
  flex-shrink: 0;
}

.contact-label {
  color: @muted;
  font-size: 0.875rem;
  font-weight: 500;
  flex-shrink: 0;
}

.contact-text {
  color: @dark;
  font-size: 0.875rem;
  flex: 1;
}

.staff-tags {
  display: flex;
  flex-wrap: wrap;
  gap: 0.5rem;
}

.staff-tag {
  padding: 0.25rem 0.5rem;
  background: rgba(0, 168, 112, 0.1);
  color: @success;
  font-size: 0.75rem;
  border-radius: 4px;
  font-weight: 500;
}

.no-staff {
  display: flex;
  align-items: center;
  gap: 1rem;
  padding: 1.5rem;
  background: rgba(107, 114, 128, 0.05);
  border-radius: 8px;
  border: 1px solid rgba(107, 114, 128, 0.1);
}

.no-staff-icon {
  color: @muted;
  font-size: 2rem;
  flex-shrink: 0;
}

.no-staff-text {
  flex: 1;
}

.no-staff-title {
  color: @muted;
  font-size: 0.875rem;
  font-weight: 500;
  margin: 0 0 0.25rem 0;
}

.no-staff-desc {
  color: @muted;
  font-size: 0.75rem;
  margin: 0;
  opacity: 0.8;
}

// 操作按钮
.booking-actions {
  display: flex;
  gap: 0.75rem;
  flex-wrap: wrap;
  margin-top: 1.5rem;
  padding-top: 1rem;
  border-top: 1px solid @border;
}

// 响应式设计
@media (max-width: 768px) {
  .booking-card {
    margin-bottom: 1rem; // 移动端减少间距
    border-radius: 8px;   // 移动端使用较小的圆角
  }

  .booking-status {
    padding: 0.75rem 1rem 0.5rem;
    flex-direction: column;
    align-items: flex-start;
    gap: 0.5rem;
  }

  .booking-content {
    padding: 0 1rem 1rem;
  }

  .service-info {
    flex-direction: column;
    gap: 0.75rem;
  }

  .service-image {
    width: 100%;
    height: 120px;
  }

  .info-row {
    grid-template-columns: 1fr;
  }

  .booking-actions {
    flex-direction: column;
  }

  .staff-info {
    flex-direction: column;
    text-align: center;
  }

  .staff-avatar {
    align-self: center;
  }

  .staff-basic {
    justify-content: center;
  }

  .staff-contact {
    align-items: center;
  }

  .no-staff {
    flex-direction: column;
    text-align: center;
  }
}
</style>
