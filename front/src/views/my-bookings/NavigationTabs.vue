<template>
  <nav class="nav-tabs">
    <div class="nav-container">
      <button
        v-for="tab in statusTabs"
        :key="tab.value"
        :class="['nav-tab', { active: activeStatus === tab.value }]"
        @click="$emit('statusChange', tab.value)"
      >
        <t-icon v-if="getTabIcon(tab.value)" :name="getTabIcon(tab.value)" class="nav-icon" />
        {{ tab.label }}
      </button>
    </div>
  </nav>
</template>

<script setup>
// Props
defineProps({
  activeStatus: {
    type: String,
    default: 'all'
  },
  statusTabs: {
    type: Array,
    default: () => []
  }
})

// Emits
defineEmits(['statusChange'])

// 获取标签图标
const getTabIcon = (status) => {
  const iconMap = {
    all: 'view-list',
    '待支付': 'money',
    '已支付': 'check-circle',
    '已取消': 'close-circle',
    '待服务': 'time',
    '服务中': 'play-circle',
    '已完成': 'check'
  }
  return iconMap[status] || 'view-list'
}
</script>

<style lang="less" scoped>
// 颜色变量
@primary: #2563eb;
@border: #e5e7eb;

// 导航标签
.nav-tabs {
  background: white;
  border-bottom: 1px solid @border;
  padding: 0 20px;
  position: sticky;
  top: 0;
  z-index: 40;
  box-shadow: 0 1px 3px 0 rgba(0, 0, 0, 0.1);
}

.nav-container {
  max-width: 1200px;
  margin: 0 auto;
  display: flex;
  gap: 8px;
  overflow-x: auto;
  padding: 16px 0;
}

.nav-tab {
  display: flex;
  align-items: center;
  gap: 8px;
  padding: 12px 20px;
  border: none;
  background: #f3f4f6;
  color: #4b5563;
  cursor: pointer;
  white-space: nowrap;
  border-radius: 50px;
  transition: all 0.2s;
  font-size: 0.95rem;
  font-weight: 500;
}

.nav-tab:hover {
  color: #374151;
  background: #e5e7eb;
}

.nav-tab.active {
  background: @primary;
  color: white;
}

.nav-icon {
  font-size: 1rem;
}

// 响应式设计
@media (max-width: 768px) {
  .nav-container {
    padding: 12px 0;
  }

  .nav-tab {
    padding: 10px 16px;
    font-size: 0.9rem;
  }
}

@media (max-width: 480px) {
  .nav-container {
    padding: 10px 0;
    gap: 6px;
  }

  .nav-tab {
    padding: 8px 12px;
    font-size: 0.85rem;
  }
}
</style>
