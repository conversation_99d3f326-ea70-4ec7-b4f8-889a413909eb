<template>
  <header class="hero-section">
    <div class="hero-container">
      <div class="hero-content">
        <h1 class="hero-title">我的预约</h1>
        <p class="hero-subtitle">管理您的所有服务预约，满足您的一切需求</p>

        <!-- 搜索框 -->
        <div class="search-container">
          <input
            type="text"
            :value="searchQuery"
            @input="$emit('update:searchQuery', $event.target.value)"
            placeholder="请输入要搜索的服务名称..."
            class="search-input"
            @keyup.enter="$emit('search')"
          />
          <button class="search-button" @click="$emit('search')">
            <svg width="20" height="20" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
              <circle cx="11" cy="11" r="8"></circle>
              <path d="m21 21-4.35-4.35"></path>
            </svg>
          </button>
        </div>
      </div>
    </div>
  </header>
</template>

<script setup>
// Props
defineProps({
  searchQuery: {
    type: String,
    default: ''
  }
})

// Emits
defineEmits(['update:searchQuery', 'search'])
</script>

<style lang="less" scoped>
// 颜色变量
@primary: #2563eb;
@primary-light: #4f46e5;

// 容器
.hero-container {
  max-width: 1200px;
  margin: 0 auto;
  padding: 0 20px;
}

// Hero头部区域
.hero-section {
  background: linear-gradient(135deg, #2563eb 0%, #4f46e5 100%);
  padding: 80px 0;
  text-align: center;
  color: white;
}

.hero-content {
  max-width: 800px;
  margin: 0 auto;
}

.hero-title {
  font-size: 2.5rem;
  font-weight: 700;
  margin-bottom: 16px;
  letter-spacing: -0.02em;
}

.hero-subtitle {
  font-size: 1.1rem;
  opacity: 0.9;
  margin-bottom: 40px;
  font-weight: 400;
  color: #bfdbfe;
}

// 搜索框
.search-container {
  display: flex;
  max-width: 500px;
  margin: 0 auto;
  background: white;
  border-radius: 50px;
  overflow: hidden;
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
}

.search-input {
  flex: 1;
  padding: 16px 24px;
  border: none;
  outline: none;
  font-size: 1rem;
  color: #333;
}

.search-input::placeholder {
  color: #999;
}

.search-button {
  background: #f97316;
  border: none;
  padding: 16px 20px;
  color: white;
  cursor: pointer;
  transition: background-color 0.2s;
}

.search-button:hover {
  background: #ea580c;
}

// 响应式设计
@media (max-width: 768px) {
  .hero-title {
    font-size: 2rem;
  }

  .hero-subtitle {
    font-size: 1rem;
  }

  .hero-section {
    padding: 60px 0;
  }

  .search-container {
    max-width: 100%;
    margin: 0 20px;
  }
}

@media (max-width: 480px) {
  .hero-container {
    padding: 0 15px;
  }

  .hero-section {
    padding: 50px 0;
  }

  .hero-title {
    font-size: 1.75rem;
  }

  .search-container {
    margin: 0 15px;
  }

  .search-input {
    padding: 14px 20px;
    font-size: 0.95rem;
  }

  .search-button {
    padding: 14px 18px;
  }
}
</style>
