<html lang="zh-CN">
<head>
  <meta charset="utf-8" />
  <meta content="width=device-width, initial-scale=1.0" name="viewport" />
  <title>家政服务详情 - 安心家政</title>
  <script src="https://res.gemcoder.com/js/reload.js"></script>
  <script src="https://cdn.tailwindcss.com"></script>
  <link
    href="https://cdn.bootcdn.net/ajax/libs/font-awesome/6.4.0/css/all.min.css"
    rel="stylesheet"
  />
  <script>
    tailwind.config = {
      theme: {
        extend: {
          colors: {
            primary: '#165DFF',
            // 主色调：蓝色，代表专业和信任
            secondary: '#0E86D4',
            // 辅助色：绿色，代表清洁和自然
            accent: '#F59E0B',
            // 强调色：橙色，用于CTA按钮
            dark: '#1F2937',
            // 深色文本
            light: '#F9FAFB',
            // 浅色背景
            muted: '#6B7280' // 次要文本
          },
          fontFamily: {
            sans: ['Inter', 'system-ui', 'sans-serif']
          }
        }
      }
    };
  </script>
  <style type="text/tailwindcss">
      @layer utilities {
          .content-auto {
              content-visibility: auto;
          }
          .text-shadow {
              text-shadow: 0 2px 4px rgba(0,0,0,0.1);
          }
          .card-hover {
              transition: all 0.3s ease;
          }
          .card-hover:hover {
              transform: translateY(-5px);
              box-shadow: 0 10px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04);
          }
          .scrollbar-hide::-webkit-scrollbar {
              display: none;
          }
          .scrollbar-hide {
              -ms-overflow-style: none;
              scrollbar-width: none;
          }
      }
  </style>
</head>
<body class="bg-gray-50 font-sans text-dark">
<!-- 主要内容 -->
<main class="container mx-auto px-4 sm:px-6 lg:px-8 py-6 md:py-8">
  <div class="flex flex-col lg:flex-row gap-6 md:gap-8">
    <!-- 左侧内容：服务详情 -->
    <div class="w-full lg:w-2/3 space-y-6">
      <!-- 服务标题和评分 -->
      <div class="bg-white rounded-xl shadow-sm p-6">
        <div
          class="flex flex-col md:flex-row md:items-start md:justify-between"
        >
          <div>
            <h1
              class="text-[clamp(1.5rem,3vw,2rem)] font-bold text-dark mb-2"
            >
              深度保洁服务
            </h1>
            <div class="flex items-center mb-4">
              <div class="flex items-center text-yellow-400 mr-3">
                <i class="fas fa-star"> </i>
                <i class="fas fa-star"> </i>
                <i class="fas fa-star"> </i>
                <i class="fas fa-star"> </i>
                <i class="fas fa-star-half-alt"> </i>
                <span class="ml-2 text-dark font-medium"> 4.8 </span>
              </div>
              <span class="text-muted text-sm"> | </span>
              <span class="text-muted text-sm ml-3">
                    已服务
                    <span class="text-primary font-medium"> 2,345 </span>
                    位客户
                  </span>
              <span class="text-muted text-sm ml-4"> | </span>
              <span class="text-muted text-sm ml-3">
                    满意度
                    <span class="text-secondary font-medium"> 98% </span>
                  </span>
            </div>
            <div class="flex flex-wrap gap-2">
                  <span
                    class="bg-primary/10 text-primary text-xs px-3 py-1 rounded-full"
                  >
                    专业团队
                  </span>
              <span
                class="bg-primary/10 text-primary text-xs px-3 py-1 rounded-full"
              >
                    持证上岗
                  </span>
              <span
                class="bg-primary/10 text-primary text-xs px-3 py-1 rounded-full"
              >
                    标准化服务
                  </span>
              <span
                class="bg-primary/10 text-primary text-xs px-3 py-1 rounded-full"
              >
                    不满意返工
                  </span>
            </div>
          </div>
          <div class="mt-4 md:mt-0 flex items-center space-x-3">
            <button
              class="flex items-center justify-center h-10 px-4 rounded-full border border-gray-300 text-muted hover:bg-gray-50 transition-colors"
            >
              <i class="fas fa-heart-o mr-2"> </i>
              <span> 收藏 </span>
            </button>
            <button
              class="flex items-center justify-center h-10 px-4 rounded-full border border-gray-300 text-muted hover:bg-gray-50 transition-colors"
            >
              <i class="fas fa-share-alt mr-2"> </i>
              <span> 分享 </span>
            </button>
          </div>
        </div>
      </div>
      <!-- 服务图片 -->
      <div class="bg-white rounded-xl shadow-sm overflow-hidden">
        <div class="w-full h-[300px] md:h-[400px] overflow-hidden">
          <img
            alt="深度保洁服务展示"
            class="w-full h-full object-cover"
            src="https://design.gemcoder.com/staticResource/echoAiSystemImages/add1639bbfed9ca4c67fb0de9037ae87.png"
          />
        </div>
      </div>
      <!-- 服务详情 -->
      <div class="bg-white rounded-xl shadow-sm p-6">
        <div class="border-b border-gray-200 pb-4 mb-6">
          <h2 class="text-xl font-bold text-dark">服务详情</h2>
        </div>
        <div class="prose max-w-none text-muted">
          <p class="mb-4">
            深度保洁服务是我们提供的专业级清洁服务，由经过严格培训的专业保洁人员上门服务，使用环保清洁剂和专业工具，对您的居家环境进行全方位、无死角的深度清洁。
          </p>
          <h3 class="text-lg font-semibold text-dark mt-6 mb-3">
            服务包含内容
          </h3>
          <ul class="list-disc pl-5 space-y-2 mb-6">
            <li>全屋除尘：包括家具、电器表面、窗台、门框等</li>
            <li>
              地面清洁：根据地面材质选择合适清洁方式，包括拖地、吸尘等
            </li>
            <li>
              厨房深度清洁：包括台面、水槽、灶台、油烟机表面、橱柜外部等
            </li>
            <li>卫生间深度清洁：包括马桶、洗手池、镜面、淋浴区、地漏等</li>
            <li>卧室整理：床铺整理、物品归位、衣柜外部清洁</li>
            <li>客厅清洁：沙发整理、茶几清洁、电视及其他电器表面清洁</li>
            <li>阳台清洁：地面清洁、栏杆擦拭、晾晒区整理</li>
          </ul>
          <h3 class="text-lg font-semibold text-dark mt-6 mb-3">
            服务时长与范围
          </h3>
          <p class="mb-4">根据房屋面积大小，我们提供不同时长的服务：</p>
          <ul class="list-disc pl-5 space-y-2 mb-6">
            <li>50㎡以下：2-3小时</li>
            <li>50-90㎡：3-4小时</li>
            <li>90-120㎡：4-5小时</li>
            <li>120-150㎡：5-6小时</li>
            <li>150㎡以上：按实际面积评估</li>
          </ul>
          <h3 class="text-lg font-semibold text-dark mt-6 mb-3">
            服务标准
          </h3>
          <p class="mb-4">
            我们承诺提供高质量的保洁服务，所有服务人员均经过严格筛选和专业培训，持证上岗。服务过程中使用环保、健康的清洁剂，确保对您和家人的健康无害。
          </p>
          <p>
            服务完成后，我们将提供服务质量检查表，您可以对服务进行评价和反馈。如对服务不满意，我们将在24小时内安排返工，直至您满意为止。
          </p>
        </div>
      </div>
      <!-- 服务流程 -->
      <div class="bg-white rounded-xl shadow-sm p-6">
        <div class="border-b border-gray-200 pb-4 mb-6">
          <h2 class="text-xl font-bold text-dark">服务流程</h2>
        </div>
        <div class="relative">
          <!-- 连接线 -->
          <div
            class="absolute left-5 top-0 bottom-0 w-0.5 bg-gray-200"
          ></div>
          <!-- 步骤1 -->
          <div class="relative pl-16 pb-8">
            <div
              class="absolute left-0 w-10 h-10 rounded-full bg-primary text-white flex items-center justify-center"
            >
              <span class="font-bold"> 1 </span>
            </div>
            <h3 class="text-lg font-semibold text-dark mb-2">在线预约</h3>
            <p class="text-muted">
              选择服务类型、时间和地址，填写房屋基本信息和特殊需求，提交预约订单。
            </p>
          </div>
          <!-- 步骤2 -->
          <div class="relative pl-16 pb-8">
            <div
              class="absolute left-0 w-10 h-10 rounded-full bg-primary text-white flex items-center justify-center"
            >
              <span class="font-bold"> 2 </span>
            </div>
            <h3 class="text-lg font-semibold text-dark mb-2">专业匹配</h3>
            <p class="text-muted">
              系统根据您的需求匹配最合适的保洁团队，提前与您确认服务详情。
            </p>
          </div>
          <!-- 步骤3 -->
          <div class="relative pl-16 pb-8">
            <div
              class="absolute left-0 w-10 h-10 rounded-full bg-primary text-white flex items-center justify-center"
            >
              <span class="font-bold"> 3 </span>
            </div>
            <h3 class="text-lg font-semibold text-dark mb-2">上门服务</h3>
            <p class="text-muted">
              保洁团队按时到达，携带专业工具和环保清洁剂，按照标准流程进行服务。
            </p>
          </div>
          <!-- 步骤4 -->
          <div class="relative pl-16 pb-8">
            <div
              class="absolute left-0 w-10 h-10 rounded-full bg-primary text-white flex items-center justify-center"
            >
              <span class="font-bold"> 4 </span>
            </div>
            <h3 class="text-lg font-semibold text-dark mb-2">质量验收</h3>
            <p class="text-muted">
              服务完成后，您对服务质量进行验收，确认无误后签字确认。
            </p>
          </div>
          <!-- 步骤5 -->
          <div class="relative pl-16">
            <div
              class="absolute left-0 w-10 h-10 rounded-full bg-primary text-white flex items-center justify-center"
            >
              <span class="font-bold"> 5 </span>
            </div>
            <h3 class="text-lg font-semibold text-dark mb-2">售后保障</h3>
            <p class="text-muted">
              服务完成后提供7天售后保障，如有任何问题可随时联系客服处理。
            </p>
          </div>
        </div>
      </div>
      <!-- 相关服务推荐 -->
      <div class="bg-white rounded-xl shadow-sm p-6">
        <div class="border-b border-gray-200 pb-4 mb-6">
          <h2 class="text-xl font-bold text-dark">相关服务推荐</h2>
        </div>
        <div class="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-4">
          <!-- 服务1 -->
          <div
            class="rounded-lg overflow-hidden border border-gray-100 shadow-sm card-hover"
          >
            <div class="relative">
              <img
                alt="开荒保洁"
                class="w-full h-40 object-cover"
                src="https://design.gemcoder.com/staticResource/echoAiSystemImages/8a8c689b58aaaa6e07e428646eaff380.png"
              />
              <div
                class="absolute top-2 right-2 bg-accent text-white text-xs px-2 py-1 rounded"
              >
                热门
              </div>
            </div>
            <div class="p-4">
              <h3 class="font-medium mb-1">开荒保洁</h3>
              <div class="flex text-yellow-400 text-xs mb-2">
                <i class="fas fa-star"> </i>
                <i class="fas fa-star"> </i>
                <i class="fas fa-star"> </i>
                <i class="fas fa-star"> </i>
                <i class="fas fa-star"> </i>
                <span class="ml-1 text-muted"> 4.9 </span>
              </div>
              <div class="flex justify-between items-center">
                <div>
                  <span class="text-primary font-bold"> ¥298 </span>
                </div>
                <a
                  class="text-primary text-sm hover:underline"
                  href="javascript:void(0);"
                >
                  查看详情
                </a>
              </div>
            </div>
          </div>
          <!-- 服务2 -->
          <div
            class="rounded-lg overflow-hidden border border-gray-100 shadow-sm card-hover"
          >
            <div class="relative">
              <img
                alt="日常保洁"
                class="w-full h-40 object-cover"
                src="https://design.gemcoder.com/staticResource/echoAiSystemImages/376dba4a9e3f8fc50bbf813eb840985e.png"
              />
            </div>
            <div class="p-4">
              <h3 class="font-medium mb-1">日常保洁</h3>
              <div class="flex text-yellow-400 text-xs mb-2">
                <i class="fas fa-star"> </i>
                <i class="fas fa-star"> </i>
                <i class="fas fa-star"> </i>
                <i class="fas fa-star"> </i>
                <i class="fas fa-star-half-alt"> </i>
                <span class="ml-1 text-muted"> 4.7 </span>
              </div>
              <div class="flex justify-between items-center">
                <div>
                  <span class="text-primary font-bold"> ¥128 </span>
                </div>
                <a
                  class="text-primary text-sm hover:underline"
                  href="javascript:void(0);"
                >
                  查看详情
                </a>
              </div>
            </div>
          </div>
          <!-- 服务3 -->
          <div
            class="rounded-lg overflow-hidden border border-gray-100 shadow-sm card-hover"
          >
            <div class="relative">
              <img
                alt="地毯清洗"
                class="w-full h-40 object-cover"
                src="https://design.gemcoder.com/staticResource/echoAiSystemImages/f422d6e0ec37d80420d4ca57b2e0e0f0.png"
              />
              <div
                class="absolute top-2 right-2 bg-secondary text-white text-xs px-2 py-1 rounded"
              >
                优惠
              </div>
            </div>
            <div class="p-4">
              <h3 class="font-medium mb-1">地毯清洗</h3>
              <div class="flex text-yellow-400 text-xs mb-2">
                <i class="fas fa-star"> </i>
                <i class="fas fa-star"> </i>
                <i class="fas fa-star"> </i>
                <i class="fas fa-star"> </i>
                <i class="far fa-star"> </i>
                <span class="ml-1 text-muted"> 4.2 </span>
              </div>
              <div class="flex justify-between items-center">
                <div>
                  <span class="text-primary font-bold"> ¥198 </span>
                </div>
                <a
                  class="text-primary text-sm hover:underline"
                  href="javascript:void(0);"
                >
                  查看详情
                </a>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
    <!-- 右侧内容：服务预订 -->
    <div class="w-full lg:w-1/3 space-y-6">
      <!-- 服务价格与预订 -->
      <div class="bg-white rounded-xl shadow-sm overflow-hidden">
        <div class="p-6 border-b border-gray-100">
          <div class="mb-4">
            <div class="text-3xl font-bold text-primary">¥368</div>
            <p class="text-muted text-sm mt-2">
              深度保洁服务（90-120㎡），服务时长4-5小时
            </p>
          </div>
        </div>
        <!-- 用户信息表单 -->
        <div class="p-6 border-b border-gray-100">
          <h3 class="text-lg font-semibold mb-4">预约信息</h3>
          <div class="space-y-4">
            <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div>
                <label class="block text-sm font-medium text-gray-700 mb-1">
                  联系人
                </label>
                <input
                  class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-primary focus:border-primary transition-colors"
                  placeholder="请输入联系人姓名"
                  type="text"
                />
              </div>
              <div>
                <label class="block text-sm font-medium text-gray-700 mb-1">
                  联系电话
                </label>
                <input
                  class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-primary focus:border-primary transition-colors"
                  placeholder="请输入联系电话"
                  type="tel"
                />
              </div>
            </div>
            <div>
              <label class="block text-sm font-medium text-gray-700 mb-1">
                服务地址
              </label>
              <input
                class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-primary focus:border-primary transition-colors"
                placeholder="请输入详细服务地址"
                type="text"
              />
            </div>
            <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div>
                <label class="block text-sm font-medium text-gray-700 mb-1">
                  服务日期
                </label>
                <input
                  class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-primary focus:border-primary transition-colors"
                  type="date"
                />
              </div>
              <div>
                <label class="block text-sm font-medium text-gray-700 mb-1">
                  服务时段
                </label>
                <select
                  class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-primary focus:border-primary transition-colors"
                >
                  <option>请选择时段</option>
                  <option>08:00-12:00</option>
                  <option>13:00-17:00</option>
                  <option>18:00-22:00</option>
                </select>
              </div>
            </div>
            <div>
              <label class="block text-sm font-medium text-gray-700 mb-1">
                特殊要求
              </label>
              <textarea
                class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-primary focus:border-primary transition-colors"
                placeholder="请输入特殊要求或注意事项"
                rows="3"
              >
                  </textarea>
            </div>
          </div>
        </div>
        <!-- 底部预订按钮 -->
        <div class="p-4 bg-gray-50">
          <button
            class="w-full py-3 bg-accent text-white rounded-lg font-medium hover:bg-accent/90 transition-colors flex items-center justify-center"
          >
            <i class="fas fa-calendar-check mr-2"> </i>
            立即预约
          </button>
          <div class="flex justify-center mt-3 space-x-4 text-sm">
            <div class="flex items-center text-muted">
              <i class="fas fa-shield-alt mr-1"> </i>
              <span> 服务保障 </span>
            </div>
            <div class="flex items-center text-muted">
              <i class="fas fa-clock mr-1"> </i>
              <span> 随时退改 </span>
            </div>
            <div class="flex items-center text-muted">
              <i class="fas fa-headphones-alt mr-1"> </i>
              <span> 7×24客服 </span>
            </div>
          </div>
        </div>
      </div>
      <!-- 服务保障 -->
      <div class="bg-white rounded-xl shadow-sm p-6">
        <h3 class="text-lg font-bold text-dark mb-4">服务保障</h3>
        <div class="space-y-4">
          <div class="flex items-start">
            <div class="bg-primary/10 p-2 rounded-full text-primary">
              <i class="fas fa-user-check"> </i>
            </div>
            <div class="ml-3">
              <h4 class="font-medium text-sm">专业认证</h4>
              <p class="text-muted text-xs mt-1">
                所有服务人员均经过严格筛选、培训和认证
              </p>
            </div>
          </div>
          <div class="flex items-start">
            <div class="bg-primary/10 p-2 rounded-full text-primary">
              <i class="fas fa-shield-alt"> </i>
            </div>
            <div class="ml-3">
              <h4 class="font-medium text-sm">服务保险</h4>
              <p class="text-muted text-xs mt-1">
                服务过程中造成的物品损坏由保险公司承担
              </p>
            </div>
          </div>
          <div class="flex items-start">
            <div class="bg-primary/10 p-2 rounded-full text-primary">
              <i class="fas fa-undo"> </i>
            </div>
            <div class="ml-3">
              <h4 class="font-medium text-sm">不满意返工</h4>
              <p class="text-muted text-xs mt-1">
                服务完成后24小时内不满意可免费返工
              </p>
            </div>
          </div>
          <div class="flex items-start">
            <div class="bg-primary/10 p-2 rounded-full text-primary">
              <i class="fas fa-hand-holding-heart"> </i>
            </div>
            <div class="ml-3">
              <h4 class="font-medium text-sm">健康保障</h4>
              <p class="text-muted text-xs mt-1">
                所有服务人员持健康证上岗，定期体检
              </p>
            </div>
          </div>
        </div>
      </div>
      <!-- 常见问题 -->
      <div class="bg-white rounded-xl shadow-sm p-6">
        <h3 class="text-lg font-bold text-dark mb-4">常见问题</h3>
        <div class="space-y-4">
          <div class="border-b border-gray-100 pb-4">
            <button
              class="flex justify-between items-center w-full text-left text-sm font-medium"
              onclick="toggleFAQ(this)"
            >
              <span> 深度保洁和日常保洁有什么区别？ </span>
              <i
                class="fas fa-chevron-down text-xs text-muted transition-transform"
              >
              </i>
            </button>
            <div class="mt-2 text-xs text-muted hidden">
              深度保洁是对房屋进行全面、彻底的清洁，包括日常保洁无法涉及的深度清洁项目，如：窗槽清洁、厨房油污深度清洁、卫生间死角清洁、家具内部除尘等。服务时间更长，清洁更彻底。日常保洁主要针对房屋表面的日常清洁维护。
            </div>
          </div>
          <div class="border-b border-gray-100 pb-4">
            <button
              class="flex justify-between items-center w-full text-left text-sm font-medium"
              onclick="toggleFAQ(this)"
            >
              <span> 服务人员会带清洁工具和清洁剂吗？ </span>
              <i
                class="fas fa-chevron-down text-xs text-muted transition-transform"
              >
              </i>
            </button>
            <div class="mt-2 text-xs text-muted hidden">
              是的，我们的服务人员会携带全套专业清洁工具和环保清洁剂上门服务，您无需准备任何清洁用品。如果您有特殊清洁剂需求，可以提前告知我们。
            </div>
          </div>
          <div class="border-b border-gray-100 pb-4">
            <button
              class="flex justify-between items-center w-full text-left text-sm font-medium"
              onclick="toggleFAQ(this)"
            >
              <span> 如何修改或取消预约？ </span>
              <i
                class="fas fa-chevron-down text-xs text-muted transition-transform"
              >
              </i>
            </button>
            <div class="mt-2 text-xs text-muted hidden">
              您可以在"我的订单"中找到相应订单，点击"修改预约"或"取消预约"按钮进行操作。服务前24小时可免费取消或修改，24小时内取消将收取30%的服务费用。
            </div>
          </div>
          <div class="border-b border-gray-100 pb-4">
            <button
              class="flex justify-between items-center w-full text-left text-sm font-medium"
              onclick="toggleFAQ(this)"
            >
              <span> 服务过程中需要有人在家吗？ </span>
              <i
                class="fas fa-chevron-down text-xs text-muted transition-transform"
              >
              </i>
            </button>
            <div class="mt-2 text-xs text-muted hidden">
              建议服务时有成年人在家，以便开门、介绍房屋情况和验收服务质量。如果您无法在家，可以提前与客服沟通，安排钥匙交接事宜。我们所有服务人员都经过严格背景调查，您可以放心。
            </div>
          </div>
          <div>
            <a
              class="text-primary text-sm flex items-center justify-center mt-2"
              href="javascript:void(0);"
            >
              查看更多常见问题
              <i class="fas fa-chevron-right ml-1 text-xs"> </i>
            </a>
          </div>
        </div>
      </div>
      <!-- 联系客服 -->
      <div class="bg-primary rounded-xl shadow-sm p-6 text-white">
        <h3 class="text-lg font-bold mb-2">有疑问？联系客服</h3>
        <p class="text-primary-100 text-sm mb-4">
          我们的客服团队将为您提供专业解答
        </p>
        <button
          class="w-full py-2 bg-white text-primary rounded-lg font-medium text-sm hover:bg-gray-100 transition-colors flex items-center justify-center"
        >
          <i class="fas fa-comment-dots mr-2"> </i>
          在线咨询
        </button>
        <div class="flex justify-center mt-4 space-x-4">
          <a
            class="bg-white/20 p-2 rounded-full hover:bg-white/30 transition-colors"
            href="javascript:void(0);"
          >
            <i class="fab fa-weixin"> </i>
          </a>
          <a
            class="bg-white/20 p-2 rounded-full hover:bg-white/30 transition-colors"
            href="javascript:void(0);"
          >
            <i class="fas fa-phone"> </i>
          </a>
          <a
            class="bg-white/20 p-2 rounded-full hover:bg-white/30 transition-colors"
            href="javascript:void(0);"
          >
            <i class="fas fa-envelope"> </i>
          </a>
        </div>
        <div class="text-center mt-4 text-xs text-primary-100">
          客服热线：************
          <br />
          服务时间：7:00-23:00
        </div>
      </div>
    </div>
  </div>
</main>
<!-- 返回顶部按钮 -->
<button
  class="fixed bottom-6 right-6 bg-primary text-white w-10 h-10 rounded-full flex items-center justify-center shadow-lg opacity-0 invisible transition-all duration-300 hover:bg-primary/90"
  id="back-to-top"
>
  <i class="fas fa-chevron-up"> </i>
</button>
<!-- JavaScript -->
<script>
  // 页面加载时初始化
  document.addEventListener('DOMContentLoaded', function () {
    // 返回顶部按钮
    var backToTopButton = document.getElementById('back-to-top');
    window.addEventListener('scroll', function () {
      if (window.scrollY > 300) {
        backToTopButton.classList.remove('opacity-0', 'invisible');
        backToTopButton.classList.add('opacity-100', 'visible');
      } else {
        backToTopButton.classList.add('opacity-0', 'invisible');
        backToTopButton.classList.remove('opacity-100', 'visible');
      }
    });
    backToTopButton.addEventListener('click', function () {
      window.scrollTo({
        top: 0,
        behavior: 'smooth'
      });
    });
  });
  // FAQ切换函数
  function toggleFAQ(button) {
    var content = button.nextElementSibling;
    var icon = button.querySelector('i');
    content.classList.toggle('hidden');
    icon.classList.toggle('transform');
    icon.classList.toggle('rotate-180');
  }
</script>
</body>
</html>
