<template>
  <!-- 全屏加载动画 -->
  <div v-if="loading" class="loading-overlay">
    <div class="loading-container">
      <div class="loading-spinner">
        <div class="spinner-ring"></div>
        <div class="spinner-ring"></div>
        <div class="spinner-ring"></div>
      </div>
      <div class="loading-text">{{ loadingText }}</div>
      <div class="loading-dots">
        <span class="dot"></span>
        <span class="dot"></span>
        <span class="dot"></span>
      </div>
    </div>
  </div>

  <div v-else class="service-detail-page">
    <!-- 主要内容 -->
    <main class="container">
      <div class="content-wrapper">
        <!-- 左侧内容：服务详情 -->
        <div class="left-content">
          <!-- 服务标题和评分 -->
          <div class="service-header">
            <div class="header-main">
              <div class="title-section">
                <h1 class="service-title">{{ serviceData.name }}</h1>
                <div class="rating-section">
                  <div class="stars">
                    <t-rate
                      :value="serviceData.star"
                      :allow-half="true"
                      readonly
                      size="18px"
                    />
                    <span class="rating-score">{{ serviceData.star }}分</span>
                  </div>
                  <span class="divider">|</span>
                  <span class="service-stats">
                    已服务 <span class="highlight">{{ serviceData.orderCount }}</span> 位客户
                  </span>
                  <span class="divider">|</span>
                  <span class="service-stats">
                    满意度 <span class="highlight-secondary">{{ 99 }}%</span>
                  </span>
                </div>
                <div class="service-badges">
                  <span
                    v-for="badge in parsedTags"
                    :key="badge"
                    class="badge"
                  >
                    {{ badge }}
                  </span>
                </div>
              </div>
            </div>
          </div>

          <!-- 服务图片 -->
          <div class="service-image-section">
            <div class="service-image-container">
              <img
                :alt="serviceData.title"
                class="service-main-image"
                :src="serviceData.image"
              />
            </div>
          </div>

          <!-- 服务详情 -->
          <div class="service-details">
            <div class="section-header">
              <h2 class="section-title">服务详情</h2>
            </div>
            <div class="details-content" v-html="serviceDetailContent">
            </div>
          </div>

          <!-- 服务流程 -->
          <ServiceProcess />
        </div>

        <!-- 右侧内容：服务预订 -->
        <div class="right-content">
          <!-- 服务价格与预订 -->
          <BookingCard :service-data="serviceData"/>

          <!-- 服务保障 -->
          <ServiceGuarantee />

          <!-- 相关服务推荐 -->
          <RelatedServices :service-data="serviceData"/>

          <!-- 常见问题 -->
          <ServiceFAQ />

          <!-- 联系客服 -->
          <ContactService />
        </div>
      </div>
    </main>

    <!-- 返回顶部按钮 -->
    <t-button
      v-show="showBackToTop"
      class="back-to-top"
      theme="primary"
      shape="circle"
      @click="scrollToTop"
    >
      <template #icon>
        <ChevronUp :size="20" />
      </template>
    </t-button>
  </div>
</template>

<script setup>
import { ref, reactive, onMounted, onUnmounted, computed, watch } from 'vue'
import { useRouter, useRoute } from 'vue-router'
import { ChevronUp } from 'lucide-vue-next'
import ServiceProcess from './ServiceProcess.vue'
import ServiceGuarantee from './ServiceGuarantee.vue'
import ServiceFAQ from './ServiceFAQ.vue'
import ContactService from './ContactService.vue'
import BookingCard from './BookingCard.vue'
import RelatedServices from './RelatedServices.vue'
import { GetServeDetail } from '@/api/serve.js'
import { MessagePlugin } from 'tdesign-vue-next';

// 响应式数据
const showBackToTop = ref(false)
const loading = ref(true)
const loadingText = ref('正在加载服务详情...')
const router = useRouter()
const route = useRoute()

// 页面挂载时获取服务详情
onMounted(async () => {
  await getServeDetail()
})

// 监听路由参数变化
watch(() => route.params.id, async (newId, oldId) => {
  if (newId && newId !== oldId) {
    console.log('路由参数变化，重新加载服务详情:', newId)
    loading.value = true
    await getServeDetail()
  }
}, { immediate: false })

const getServeDetail = async () => {
  const serveId = route.params.id
  console.log('获取服务详情，ID:', serveId)

  if (!serveId) {
    await MessagePlugin.error("服务编号不存在")
    return
  }
  const response = await GetServeDetail(serveId)
  if(response.code !== 200) {
    await MessagePlugin.error(response.msg || "获取服务详情失败")
    return
  }
  serviceData.value = response.data || {}
  loading.value = false
}

// 解析tag字段的计算属性
const parsedTags = computed(() => {
  if (!serviceData.value.tag) return []
  try {
    // 如果tag是字符串格式的JSON数组，解析它
    if (typeof serviceData.value.tag === 'string') {
      // 尝试解析JSON格式
      if (serviceData.value.tag.startsWith('[') && serviceData.value.tag.endsWith(']')) {
        return JSON.parse(serviceData.value.tag)
      }
      // 如果是逗号分隔的字符串
      if (serviceData.value.tag.includes(',')) {
        return serviceData.value.tag.split(',').map(tag => tag.trim()).filter(tag => tag)
      }
      // 如果是单个标签
      return [serviceData.value.tag]
    } else if (Array.isArray(serviceData.value.tag)) {
      return serviceData.value.tag
    }
  } catch (error) {
    console.warn('解析服务标签失败:', error)
    // 如果解析失败，尝试按逗号分割
    if (typeof serviceData.value.tag === 'string') {
      return serviceData.value.tag.split(',').map(tag => tag.trim()).filter(tag => tag)
    }
  }
  return []
})

// 处理服务详情内容的计算属性
const serviceDetailContent = computed(() => {
  // 尝试多个可能的字段名
  const content = serviceData.value.descriptionDetail ||
                 serviceData.value.description ||
                 serviceData.value.detailContent ||
                 serviceData.value.content ||
                 ''

  // 如果内容包含HTML标签但显示为纯文本，可能需要解码
  if (content && typeof content === 'string') {
    let decodedContent = content

    // 检查是否是被编码的HTML
    if (content.includes('&lt;') || content.includes('&gt;')) {
      // 解码HTML实体
      decodedContent = content
        .replace(/&lt;/g, '<')
        .replace(/&gt;/g, '>')
        .replace(/&amp;/g, '&')
        .replace(/&quot;/g, '"')
        .replace(/&#39;/g, "'")
        .replace(/<br>/g, '\n')
        .replace(/<br\/>/g, '\n')
        .replace(/<br \/>/g, '\n')
    }
    return decodedContent
  }
  return ''
})

// Mock数据
const serviceData = ref({})
const handleScroll = () => {
  showBackToTop.value = window.scrollY > 300
}

const scrollToTop = () => {
  window.scrollTo({
    top: 0,
    behavior: 'smooth'
  })
}

// 模拟数据加载
const loadServiceData = async () => {
  // 模拟API调用延迟
  await new Promise(resolve => setTimeout(resolve, 2000))
  loading.value = false
}

// 生命周期
onMounted(() => {
  window.addEventListener('scroll', handleScroll)
  // 注释掉模拟数据加载，使用真实API
  // loadServiceData()
})

onUnmounted(() => {
  window.removeEventListener('scroll', handleScroll)
})
</script>

<style lang="less" scoped>
// 颜色变量
@primary: #165dff;
@secondary: #0e86d4;
@accent: #f59e0b;
@price-color: #ff4f24;
@dark: #1f2937;
@light: #f9fafb;
@muted: #6b7280;
@border: #e5e7eb;

// 全屏加载动画样式
.loading-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: linear-gradient(135deg, rgba(37, 99, 235, 0.1) 0%, rgba(79, 70, 229, 0.1) 100%);
  backdrop-filter: blur(10px);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 9999;
  animation: overlayFadeIn 0.4s ease-out;
}

.loading-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 24px;
  animation: containerSlideIn 0.6s ease-out;
}

// 自定义旋转加载器
.loading-spinner {
  position: relative;
  width: 80px;
  height: 80px;
}

.spinner-ring {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  border: 3px solid transparent;
  border-radius: 50%;
  animation: spin 2s linear infinite;

  &:nth-child(1) {
    border-top-color: #2563eb;
    animation-duration: 2s;
  }

  &:nth-child(2) {
    border-right-color: #3b82f6;
    animation-duration: 1.5s;
    animation-direction: reverse;
    width: 90%;
    height: 90%;
    top: 5%;
    left: 5%;
  }

  &:nth-child(3) {
    border-bottom-color: #60a5fa;
    animation-duration: 1s;
    width: 80%;
    height: 80%;
    top: 10%;
    left: 10%;
  }
}

// 加载文字
.loading-text {
  font-size: 18px;
  font-weight: 600;
  color: #1f2937;
  text-align: center;
  letter-spacing: 0.5px;
  margin: 0;
  text-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

// 跳动的点
.loading-dots {
  display: flex;
  gap: 8px;
  align-items: center;
}

.dot {
  width: 8px;
  height: 8px;
  background: #2563eb;
  border-radius: 50%;
  animation: dotBounce 1.4s ease-in-out infinite both;

  &:nth-child(1) {
    animation-delay: -0.32s;
  }

  &:nth-child(2) {
    animation-delay: -0.16s;
  }

  &:nth-child(3) {
    animation-delay: 0s;
  }
}

// 动画定义
@keyframes overlayFadeIn {
  from {
    opacity: 0;
  }
  to {
    opacity: 1;
  }
}

@keyframes containerSlideIn {
  from {
    opacity: 0;
    transform: translateY(30px) scale(0.9);
  }
  to {
    opacity: 1;
    transform: translateY(0) scale(1);
  }
}

@keyframes spin {
  from {
    transform: rotate(0deg);
  }
  to {
    transform: rotate(360deg);
  }
}

@keyframes dotBounce {
  0%, 80%, 100% {
    transform: scale(0.8);
    opacity: 0.5;
  }
  40% {
    transform: scale(1.2);
    opacity: 1;
  }
}

// 响应式设计
@media (max-width: 768px) {
  .loading-spinner {
    width: 60px;
    height: 60px;
  }

  .loading-text {
    font-size: 16px;
  }

  .dot {
    width: 6px;
    height: 6px;
  }
}

.service-detail-page {
  background: #f8f9fa;
  font-family: 'Inter', system-ui, sans-serif;
  color: @dark;
  min-height: 100vh;
}

.container {
  max-width: 1400px;
  margin: 0 auto;
  padding: 24px 16px;

  @media (min-width: 640px) {
    padding: 24px 24px;
  }

  @media (min-width: 1024px) {
    padding: 32px 32px;
  }
}

.content-wrapper {
  display: flex;
  flex-direction: column;
  gap: 24px;

  @media (min-width: 1024px) {
    flex-direction: row;
    gap: 32px;
  }
}

.left-content {
  width: 100%;
  display: flex;
  flex-direction: column;
  gap: 24px;

  @media (min-width: 1024px) {
    width: 66.666667%;
  }
}

.right-content {
  width: 100%;
  display: flex;
  flex-direction: column;
  gap: 24px;

  @media (min-width: 1024px) {
    width: 33.333333%;
  }
}

// 通用卡片样式
.service-header,
.service-image-section,
.service-details {
  background: white;
  border-radius: 12px;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
  overflow: hidden;
}

// 服务标题和评分
.service-header {
  padding: 24px;
}

.header-main {
  display: flex;
  flex-direction: column;
  gap: 16px;
}

.service-title {
  font-size: clamp(1.5rem, 3vw, 2rem);
  font-weight: bold;
  color: @dark;
  margin: 0 0 8px 0;
}

.rating-section {
  display: flex;
  align-items: center;
  margin-bottom: 16px;
  flex-wrap: wrap;
  gap: 8px;
}

.stars {
  display: flex;
  align-items: center;
  margin-right: 12px;
  gap: 8px;
}

.rating-score {
  margin-left: 8px;
  color: @dark;
  font-weight: 500;
}

.divider {
  color: @muted;
  font-size: 14px;
  margin: 0 12px;
}

.service-stats {
  color: @muted;
  font-size: 14px;
}

.highlight {
  color: @primary;
  font-weight: 500;
}

.highlight-secondary {
  color: @secondary;
  font-weight: 500;
}

.service-badges {
  display: flex;
  flex-wrap: wrap;
  gap: 8px;
}

.badge {
  background: rgba(22, 93, 255, 0.1);
  color: @primary;
  font-size: 12px;
  padding: 4px 12px;
  border-radius: 9999px;
}

// 删除action-buttons样式，因为已经移除了收藏和分享按钮

// 服务图片
.service-image-container {
  width: 100%;
  height: 300px;
  overflow: hidden;

  @media (min-width: 768px) {
    height: 400px;
  }
}

.service-main-image {
  width: 100%;
  height: 100%;
  object-fit: cover;
}

// 服务详情
.service-details,
.service-process {
  padding: 24px;
}

.section-header {
  border-bottom: 1px solid @border;
  padding-bottom: 16px;
  margin-bottom: 24px;
}

.section-title {
  font-size: 20px;
  font-weight: bold;
  color: @dark;
  margin: 0;
}

.details-content {
  max-width: none;
  color: @muted;
  line-height: 1.6;

  // 使用深度选择器为v-html内容应用样式
  :deep(.description) {
    margin-bottom: 16px;
    line-height: 1.6;
    color: @muted;
  }

  :deep(.sub-title) {
    font-size: 18px;
    font-weight: 600;
    color: @dark;
    margin: 24px 0 12px 0;
  }

  :deep(.service-list) {
    list-style: disc;
    padding-left: 20px;
    margin-bottom: 24px;

    li {
      margin-bottom: 8px;
      line-height: 1.5;
      color: @muted;
    }
  }

  // 为解码后的HTML内容添加样式
  :deep(h1) {
    font-size: 24px;
    font-weight: bold;
    color: @dark;
    margin: 24px 0 16px 0;
    border-bottom: 2px solid @primary;
    padding-bottom: 8px;
  }

  :deep(h2) {
    font-size: 20px;
    font-weight: 600;
    color: @dark;
    margin: 20px 0 12px 0;
  }

  :deep(h3) {
    font-size: 18px;
    font-weight: 600;
    color: @dark;
    margin: 16px 0 8px 0;
  }

  :deep(p) {
    margin-bottom: 12px;
    line-height: 1.6;
    color: @muted;
  }

  :deep(ul) {
    list-style: disc;
    padding-left: 20px;
    margin-bottom: 16px;

    li {
      margin-bottom: 6px;
      line-height: 1.5;
      color: @muted;
    }
  }

  :deep(ol) {
    list-style: decimal;
    padding-left: 20px;
    margin-bottom: 16px;

    li {
      margin-bottom: 6px;
      line-height: 1.5;
      color: @muted;
    }
  }

  :deep(strong) {
    font-weight: 600;
    color: @dark;
  }

  :deep(em) {
    font-style: italic;
    color: @muted;
  }
}




// 返回顶部按钮
.back-to-top {
  position: fixed;
  bottom: 24px;
  right: 24px;
  width: 40px;
  height: 40px;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
  z-index: 1000;
  transition: all 0.3s ease;

  &:hover {
    transform: translateY(-2px);
    box-shadow: 0 6px 16px rgba(0, 0, 0, 0.2);
  }
}

// 图标样式已使用 lucide-vue-next 组件替代

// 响应式调整
@media (max-width: 768px) {
  .content-wrapper {
    gap: 16px;
  }

  .left-content,
  .right-content {
    gap: 16px;
  }

  .service-header,
  .service-details {
    padding: 16px;
  }

  .service-title {
    font-size: 1.5rem;
  }

  .services-grid {
    grid-template-columns: 1fr;
  }

  .back-to-top {
    bottom: 16px;
    right: 16px;
  }
}
</style>
