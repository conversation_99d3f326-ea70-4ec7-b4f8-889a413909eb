<template>
  <div class="test-page">
    <div class="test-header">
      <h1>相关服务推荐测试页面</h1>
      <div class="test-controls">
        <t-button @click="toggleEmptyState">
          {{ isEmpty ? '显示服务列表' : '显示空状态' }}
        </t-button>
        <t-button @click="changeServiceData" theme="primary">
          切换服务数据
        </t-button>
      </div>
    </div>
    
    <div class="test-content">
      <RelatedServices :service-data="currentServiceData" />
    </div>
    
    <div class="debug-info">
      <h3>调试信息</h3>
      <pre>{{ JSON.stringify(currentServiceData, null, 2) }}</pre>
    </div>
  </div>
</template>

<script setup>
import { ref, computed } from 'vue'
import RelatedServices from './RelatedServices.vue'

// 控制是否显示空状态
const isEmpty = ref(false)
const currentServiceIndex = ref(0)

// 模拟服务数据
const mockServiceDataList = [
  {
    serveId: 1,
    name: '深度清洁服务',
    category: '清洁服务',
    image: 'https://via.placeholder.com/300x200?text=深度清洁',
    star: 4.8,
    price: 199,
    unit: '元/次'
  },
  {
    serveId: 2,
    name: '家电维修服务',
    category: '维修服务',
    image: 'https://via.placeholder.com/300x200?text=家电维修',
    star: 4.6,
    price: 150,
    unit: '元/次'
  },
  {
    serveId: 3,
    name: '管道疏通服务',
    category: '维修服务',
    image: 'https://via.placeholder.com/300x200?text=管道疏通',
    star: 4.7,
    price: 120,
    unit: '元/次'
  }
]

// 当前显示的服务数据
const currentServiceData = computed(() => {
  if (isEmpty.value) {
    return { serveId: 999, name: '空服务', category: '不存在的分类' }
  }
  return mockServiceDataList[currentServiceIndex.value]
})

// 切换空状态
const toggleEmptyState = () => {
  isEmpty.value = !isEmpty.value
}

// 切换服务数据
const changeServiceData = () => {
  currentServiceIndex.value = (currentServiceIndex.value + 1) % mockServiceDataList.length
}
</script>

<style lang="less" scoped>
.test-page {
  padding: 24px;
  max-width: 1200px;
  margin: 0 auto;
}

.test-header {
  margin-bottom: 32px;
  text-align: center;
  
  h1 {
    font-size: 24px;
    color: #1f2937;
    margin-bottom: 16px;
  }
}

.test-controls {
  display: flex;
  justify-content: center;
  gap: 16px;
  margin-bottom: 24px;
}

.test-content {
  background: #f8fafc;
  padding: 24px;
  border-radius: 8px;
  margin-bottom: 24px;
}

.debug-info {
  background: #f3f4f6;
  padding: 16px;
  border-radius: 8px;
  
  h3 {
    margin-top: 0;
    margin-bottom: 12px;
    color: #374151;
  }
  
  pre {
    background: white;
    padding: 12px;
    border-radius: 4px;
    font-size: 12px;
    overflow-x: auto;
  }
}
</style>
