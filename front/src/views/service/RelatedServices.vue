<template>
  <div class="related-services">
    <div class="section-header">
      <h2 class="section-title">相关服务推荐</h2>
    </div>

    <!-- 有相关服务时显示服务列表 -->
    <div v-if="relatedServices.length > 0" class="services-grid">
      <div
        v-for="service in relatedServices"
        :key="service.serveId"
        class="service-card"
        @click="handleServiceClick(service)"
      >
        <div class="service-image">
          <img
            :alt="service.name"
            class="service-img"
            :src="service.image"
          />
          <div
            v-if="service.badge"
            class="service-badge"
            :class="service.badgeType"
          >
            {{ service.badge }}
          </div>
        </div>
        <div class="service-info">
          <h3 class="service-name">{{ service.name }}</h3>
          <div class="service-rating">
            <t-rate
              :value="service.star"
              :allow-half="true"
              readonly
              size="12px"
            />
            <span class="rating-text">{{ service.star }}</span>
          </div>
          <div class="service-footer">
            <div class="price">
              <span class="price-symbol">¥</span>
              <span class="price-amount">{{ service.price }}</span>
              <span class="price-unit-small">{{ service.unit || '元/次' }}</span>
            </div>
            <a href="javascript:void(0);" class="view-detail" @click.stop="handleServiceClick(service)">查看详情</a>
          </div>
        </div>
      </div>
    </div>

    <!-- 无相关服务时显示空状态 -->
    <div v-else class="empty-state">
      <div class="empty-icon">
        <t-icon name="service" size="48px" />
      </div>
      <div class="empty-content">
        <h3 class="empty-title">暂无相关服务推荐</h3>
        <p class="empty-description">
          很抱歉，当前没有找到相关的服务推荐。<br>
          您可以浏览其他服务分类或联系客服获取帮助。
        </p>
        <div class="empty-actions">
          <t-button theme="primary" @click="goToAllServices">
            浏览全部服务
          </t-button>
          <t-button variant="outline" @click="contactService">
            联系客服
          </t-button>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref, computed, onMounted, watch } from 'vue'
import { useRouter } from 'vue-router'
import { MessagePlugin } from 'tdesign-vue-next';
import { ListServe } from '@/api/serve.js'

const router = useRouter()

// Props
const props = defineProps({
  serviceData: {
    type: Object,
    required: true
  }
})

// 相关服务数据
const relatedServices = ref([])

const loadRelatedServices = async () => {
  try {
    console.log('加载相关服务，当前服务数据:', props.serviceData)
    const response = await ListServe({
      pageNum: 1,
      pageSize: 6,
      category: props.serviceData.category
    })
    console.log('相关服务API响应:', response)
    if(response.code !== 200){
      await MessagePlugin.error(response.msg || "推荐服务列表加载失败")
      return
    }
    // 过滤掉当前服务
    const filteredServices = response.rows.filter(service => service.serveId !== props.serviceData.serveId)
    console.log('过滤后的相关服务:', filteredServices)
    relatedServices.value = filteredServices
  } catch (error) {
    console.error('加载相关服务失败:', error)
    await MessagePlugin.error("加载相关服务失败")
  }
}

onMounted(async () => {
  if (props.serviceData && props.serviceData.category) {
    await loadRelatedServices()
  }
})

// 监听 serviceData 变化
watch(() => props.serviceData, async (newServiceData, oldServiceData) => {
  if (newServiceData && newServiceData.category &&
      newServiceData.serveId !== oldServiceData?.serveId) {
    console.log('服务数据变化，重新加载相关服务:', newServiceData)
    await loadRelatedServices()
  }
}, { deep: true })

// 处理服务卡片点击
const handleServiceClick = (service) => {
  console.log('点击服务卡片:', service)
  console.log('跳转路径:', `/service/${service.serveId}`)
  router.push(`/service/${service.serveId}`)
}

// 跳转到全部服务页面
const goToAllServices = () => {
  router.push('/services')
}

// 联系客服
const contactService = async () => {
  await MessagePlugin.warning("客服功能开发中，请耐心等待")
}
</script>

<style lang="less" scoped>
// 颜色变量
@primary: #165dff;
@secondary: #0e86d4;
@accent: #f59e0b;
@price-color: #ff4f24;
@dark: #1f2937;
@muted: #6b7280;
@border: #e5e7eb;

.related-services {
  background: white;
  border-radius: 12px;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
  overflow: hidden;
  padding: 24px;
}

.section-header {
  border-bottom: 1px solid @border;
  padding-bottom: 16px;
  margin-bottom: 24px;
}

.section-title {
  font-size: 20px;
  font-weight: bold;
  color: @dark;
  margin: 0;
}

.services-grid {
  display: grid;
  grid-template-columns: 1fr;
  gap: 16px;

  @media (min-width: 640px) {
    grid-template-columns: repeat(2, 1fr);
  }

  @media (min-width: 1024px) {
    grid-template-columns: repeat(2, 1fr);
  }
}

.service-card {
  border-radius: 8px;
  overflow: hidden;
  border: 1px solid #f0f0f0;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
  transition: all 0.3s ease;
  cursor: pointer;

  &:hover {
    transform: translateY(-5px);
    box-shadow: 0 10px 25px rgba(0, 0, 0, 0.1);
  }
}

.service-image {
  position: relative;
  width: 100%;
  height: 160px;
  overflow: hidden;
}

.service-img {
  width: 100%;
  height: 100%;
  object-fit: cover;
}

.service-badge {
  position: absolute;
  top: 8px;
  right: 8px;
  padding: 4px 8px;
  border-radius: 4px;
  font-size: 12px;
  font-weight: 600;
  color: white;

  &.hot {
    background: @accent;
  }

  &.discount {
    background: @secondary;
  }
}

.service-info {
  padding: 16px;
}

.service-name {
  font-weight: 500;
  margin: 0 0 8px 0;
  font-size: 14px;
}

.service-rating {
  display: flex;
  align-items: center;
  margin-bottom: 12px;
  font-size: 12px;
  gap: 4px;
}

.rating-text {
  color: @muted;
}

.service-footer {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.price {
  display: flex;
  align-items: baseline;
  gap: 2px;
}

.price-symbol {
  color: @price-color;
  font-weight: bold;
  font-size: 14px;
}

.price-amount {
  color: @price-color;
  font-weight: bold;
  font-size: 18px;
}

.price-unit-small {
  color: @price-color;
  font-size: 10px;
  font-weight: normal;
}

.view-detail {
  color: @primary;
  font-size: 12px;
  text-decoration: none;

  &:hover {
    text-decoration: underline;
  }
}

// 空状态样式
.empty-state {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 60px 20px;
  text-align: center;
}

.empty-icon {
  margin-bottom: 24px;
  color: @muted;
  opacity: 0.6;
}

.empty-content {
  max-width: 400px;
}

.empty-title {
  font-size: 18px;
  font-weight: 600;
  color: @dark;
  margin: 0 0 12px 0;
}

.empty-description {
  font-size: 14px;
  color: @muted;
  line-height: 1.6;
  margin: 0 0 32px 0;
}

.empty-actions {
  display: flex;
  gap: 12px;
  justify-content: center;
  flex-wrap: wrap;
}

// 响应式调整
@media (max-width: 768px) {
  .related-services {
    padding: 16px;
  }

  .services-grid {
    grid-template-columns: 1fr;
  }

  .empty-state {
    padding: 40px 16px;
  }

  .empty-actions {
    flex-direction: column;
    align-items: center;

    .t-button {
      width: 100%;
      max-width: 200px;
    }
  }
}
</style>
