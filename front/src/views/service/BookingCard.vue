<template>
  <div class="booking-card">
    <div class="price-section">
      <div class="main-price">
        ¥{{ serviceData.price }}
        <span class="price-unit">{{serviceData.unit}}/{{serviceData.serviceUnit}}</span>
      </div>
      <p class="price-description">
        {{ serviceData.description }}
      </p>
    </div>

    <!-- 用户信息表单 -->
    <div class="booking-form">
      <h3 class="form-title">预约信息</h3>
      <t-form ref="formRef" :data="bookingForm" :rules="formRules" @submit="handleBooking">
        <div class="form-content">
          <t-form-item label="联系人" name="contactName" required>
            <t-input
              v-model="bookingForm.contactName"
              placeholder="请输入联系人姓名"
              class="form-input"
            />
          </t-form-item>

          <t-form-item label="联系电话" name="phone" required>
            <t-input
              v-model="bookingForm.phone"
              placeholder="请输入联系电话"
              type="tel"
              class="form-input"
            />
          </t-form-item>

          <t-form-item label="服务地址" name="address" required>
            <t-textarea
              v-model="bookingForm.address"
              placeholder="请输入详细服务地址"
              :autosize="{ minRows: 3, maxRows: 5 }"
            />
          </t-form-item>

          <t-form-item label="服务日期" name="serviceDate" required>
            <t-date-picker
              v-model="bookingForm.serviceDate"
              placeholder="请选择日期"
              class="form-input"
              clearable
              :disable-date="disableDate"
              style="width: 100%"
            />
          </t-form-item>

          <t-form-item label="服务时段" name="serviceTime" required>
            <t-select
              v-model="bookingForm.serviceTime"
              :options="serviceTimeOptions"
              placeholder="请选择预约时段"
              class="form-input"
              style="width: 100%"
            />
          </t-form-item>

          <t-form-item label="特殊要求" name="special">
            <t-textarea
              v-model="bookingForm.special"
              placeholder="请输入特殊要求或注意事项（选填）"
              :autosize="{ minRows: 3, maxRows: 5 }"
            />
          </t-form-item>
        </div>
      </t-form>
    </div>

    <!-- 底部预订按钮 -->
    <div class="booking-footer">
      <t-button
        theme="warning"
        size="large"
        block
        type="submit"
        form="booking-form"
        :loading="submitLoading"
        class="booking-btn"
        @click="submitForm"
      >
        <template #icon>
          <Calendar :size="18" />
        </template>
        立即预约
      </t-button>
      <div class="service-guarantees">
        <div class="guarantee-item">
          <Shield :size="14" />
          <span>服务保障</span>
        </div>
        <div class="guarantee-item">
          <Clock :size="14" />
          <span>随时退改</span>
        </div>
        <div class="guarantee-item">
          <Headphones :size="14" />
          <span>7×24客服</span>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup>
import { reactive, ref } from 'vue'
import { MessagePlugin } from 'tdesign-vue-next'
import {
  Calendar,
  Clock,
  Headphones,
  Shield
} from 'lucide-vue-next'
import { addReservation } from '@/api/reservation'
import { useRoute,useRouter} from 'vue-router'
import { useUserStore } from '@/store/modules/user.js'

const route = useRoute()
const router = useRouter()
const userStore = useUserStore()
// Props
const props = defineProps({
  serviceData: {
    type: Object,
    required: true
  }
})

// 响应式数据
const formRef = ref(null)
const submitLoading = ref(false)

// 预约表单数据
const bookingForm = reactive({
  contactName: '',
  phone: '',
  address: '',
  serviceDate: '',
  serviceTime: '',
  special: '',
  serveId: route.params.id,
  customerId: userStore.userId
})

// 表单校验规则
const formRules = {
  contactName: [
    { required: true, message: '请输入联系人姓名', type: 'error' },
    { min: 2, max: 20, message: '姓名长度应在2-20个字符之间', type: 'error' }
  ],
  phone: [
    { required: true, message: '请输入联系电话', type: 'error' },
    { pattern: /^1[3-9]\d{9}$/, message: '请输入正确的手机号码', type: 'error' }
  ],
  address: [
    { required: true, message: '请输入服务地址', type: 'error' },
    { min: 5, message: '地址至少5个字符', type: 'error' }
  ],
  serviceDate: [
    { required: true, message: '请选择服务日期', type: 'error' }
  ],
  serviceTime: [
    { required: true, message: '请选择服务时段', type: 'error' }
  ]
}

const serviceTimeOptions = ref([
  { label: "08:00-10:00", value: "08:00-10:00" },
  { label: "10:00-12:00", value: "10:00-12:00" },
  { label: "12:00-14:00", value: "12:00-14:00" },
  { label: "14:00-16:00", value: "14:00-16:00" },
  { label: "16:00-18:00", value: "16:00-18:00" },
  { label: "18:00-20:00", value: "18:00-20:00" },
  { label: "20:00-22:00", value: "20:00-22:00" },
])

// 禁用过去的日期
const disableDate = (date) => {
  const today = new Date()
  today.setHours(0, 0, 0, 0)
  return date < today
}

// 提交表单
const submitForm = async () => {
  const result = await formRef.value?.validate()
  if( result !== true) {
    await MessagePlugin.error('请将信息填写完整后在提交！')
    return
  }
  await handleBooking()
}

// 处理预约
const handleBooking = async () => {
  // 如果是通过表单提交事件触发的，检查验证结果
  submitLoading.value = true
  const response = await addReservation(bookingForm)
  if( response.code !== 200) {
    await MessagePlugin.error(response.msg || '预约失败！请稍后再试')
    submitLoading.value = false
    return
  }
  submitLoading.value = false
  await MessagePlugin.success('预约成功！')
  await router.push('/my-bookings')
}

// 重置表单
const resetForm = () => {
  formRef.value?.reset()
}
</script>

<style lang="less" scoped>
// 颜色变量
@primary: #165dff;
@price-color: #ff4f24;
@dark: #1f2937;
@light: #f9fafb;
@muted: #6b7280;
@border: #e5e7eb;

.booking-card {
  background: white;
  border-radius: 12px;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
  overflow: hidden;
}

.price-section {
  padding: 24px;
  border-bottom: 1px solid @border;
}

.main-price {
  font-size: 48px;
  font-weight: bold;
  color: @price-color;
  line-height: 1;
  display: flex;
  align-items: baseline;
  gap: 8px;
}

.price-unit {
  font-size: 16px;
  font-weight: normal;
  color: @price-color;
}

.price-description {
  color: @muted;
  font-size: 14px;
  margin: 8px 0 0 0;
}

.booking-form {
  padding: 24px;
  border-bottom: 1px solid @border;
}

.form-title {
  font-size: 18px;
  font-weight: 600;
  margin: 0 0 16px 0;
}

.form-content {
  display: flex;
  flex-direction: column;
  gap: 4px;
}

// t-form 样式调整
:deep(.t-form-item) {
  margin-bottom: 4px;

  .t-form-item__label {
    font-size: 14px;
    font-weight: 500;
    color: #374151;
    margin-bottom: 4px;
    padding: 0;
    line-height: 1.4;
  }

  .t-form-item__content {
    margin: 0;
  }

  .t-form-item__help {
    margin-top: 4px;
    font-size: 12px;
  }

  &:last-child {
    margin-bottom: 0;
  }
}

// 确保所有表单组件宽度一致
:deep(.t-input),
:deep(.t-textarea),
:deep(.t-date-picker),
:deep(.t-select) {
  width: 100% !important;
}

.booking-footer {
  padding: 16px;
  background: @light;
}

.booking-btn {
  margin-bottom: 12px;
}

.service-guarantees {
  display: flex;
  justify-content: center;
  gap: 16px;
  font-size: 12px;
}

.guarantee-item {
  display: flex;
  align-items: center;
  color: @muted;
  gap: 4px;
}

// 响应式调整
@media (max-width: 768px) {
  .booking-card {
    padding: 16px;
  }

  .main-price {
    font-size: 36px;
  }

  .price-unit {
    font-size: 14px;
  }


}
</style>
