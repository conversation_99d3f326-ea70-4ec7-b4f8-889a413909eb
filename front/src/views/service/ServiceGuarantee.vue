<template>
  <div class="service-guarantee">
    <h3 class="guarantee-title">服务保障</h3>
    <div class="guarantee-list">
      <div class="guarantee-item-detail">
        <div class="guarantee-icon">
          <Award :size="20" />
        </div>
        <div class="guarantee-content">
          <h4 class="guarantee-name">专业认证</h4>
          <p class="guarantee-desc">
            所有服务人员均经过严格筛选、培训和认证
          </p>
        </div>
      </div>
      <div class="guarantee-item-detail">
        <div class="guarantee-icon">
          <Shield :size="20" />
        </div>
        <div class="guarantee-content">
          <h4 class="guarantee-name">服务保险</h4>
          <p class="guarantee-desc">
            服务过程中造成的物品损坏由保险公司承担
          </p>
        </div>
      </div>
      <div class="guarantee-item-detail">
        <div class="guarantee-icon">
          <RotateCcw :size="20" />
        </div>
        <div class="guarantee-content">
          <h4 class="guarantee-name">不满意返工</h4>
          <p class="guarantee-desc">
            服务完成后24小时内不满意可免费返工
          </p>
        </div>
      </div>
      <div class="guarantee-item-detail">
        <div class="guarantee-icon">
          <Heart :size="20" />
        </div>
        <div class="guarantee-content">
          <h4 class="guarantee-name">健康保障</h4>
          <p class="guarantee-desc">
            所有服务人员持健康证上岗，定期体检
          </p>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup>
import {
  Award,
  Shield,
  RotateCcw,
  Heart
} from 'lucide-vue-next'
</script>

<style lang="less" scoped>
// 颜色变量
@primary: #165dff;
@dark: #1f2937;
@muted: #6b7280;

.service-guarantee {
  background: white;
  border-radius: 12px;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
  overflow: hidden;
  padding: 24px;
}

.guarantee-title {
  font-size: 18px;
  font-weight: bold;
  color: @dark;
  margin: 0 0 16px 0;
}

.guarantee-list {
  display: flex;
  flex-direction: column;
  gap: 16px;
}

.guarantee-item-detail {
  display: flex;
  align-items: flex-start;
  gap: 12px;
}

.guarantee-icon {
  background: rgba(22, 93, 255, 0.1);
  padding: 8px;
  border-radius: 50%;
  color: @primary;
  flex-shrink: 0;
}

.guarantee-content {
  flex: 1;
}

.guarantee-name {
  font-weight: 500;
  font-size: 14px;
  margin: 0 0 4px 0;
}

.guarantee-desc {
  color: @muted;
  font-size: 12px;
  margin: 0;
  line-height: 1.4;
}

// 响应式调整
@media (max-width: 768px) {
  .service-guarantee {
    padding: 16px;
  }
}
</style>
