<template>
  <div class="service-process">
    <div class="section-header">
      <h2 class="section-title">服务流程</h2>
    </div>
    <div class="process-container">
      <div class="process-line"></div>

      <div class="process-step">
        <div class="step-number">1</div>
        <h3 class="step-title">在线预约</h3>
        <p class="step-description">
          选择服务类型、时间和地址，填写房屋基本信息和特殊需求，提交预约订单。
        </p>
      </div>

      <div class="process-step">
        <div class="step-number">2</div>
        <h3 class="step-title">专业匹配</h3>
        <p class="step-description">
          系统根据您的需求匹配最合适的保洁团队，提前与您确认服务详情。
        </p>
      </div>

      <div class="process-step">
        <div class="step-number">3</div>
        <h3 class="step-title">上门服务</h3>
        <p class="step-description">
          保洁团队按时到达，携带专业工具和环保清洁剂，按照标准流程进行服务。
        </p>
      </div>

      <div class="process-step">
        <div class="step-number">4</div>
        <h3 class="step-title">质量验收</h3>
        <p class="step-description">
          服务完成后，您对服务质量进行验收，确认无误后签字确认。
        </p>
      </div>

      <div class="process-step">
        <div class="step-number">5</div>
        <h3 class="step-title">售后保障</h3>
        <p class="step-description">
          服务完成后提供7天售后保障，如有任何问题可随时联系客服处理。
        </p>
      </div>
    </div>
  </div>
</template>

<script setup>
// 无需额外的逻辑
</script>

<style lang="less" scoped>
// 颜色变量
@primary: #165dff;
@dark: #1f2937;
@muted: #6b7280;
@border: #e5e7eb;

.service-process {
  background: white;
  border-radius: 12px;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
  overflow: hidden;
  padding: 24px;
}

.section-header {
  border-bottom: 1px solid @border;
  padding-bottom: 16px;
  margin-bottom: 24px;
}

.section-title {
  font-size: 20px;
  font-weight: bold;
  color: @dark;
  margin: 0;
}

.process-container {
  position: relative;
}

.process-line {
  position: absolute;
  left: 20px;
  top: 0;
  bottom: 0;
  width: 2px;
  background: @border;
}

.process-step {
  position: relative;
  padding-left: 64px;
  padding-bottom: 32px;

  &:last-child {
    padding-bottom: 0;
  }
}

.step-number {
  position: absolute;
  left: 0;
  width: 40px;
  height: 40px;
  border-radius: 50%;
  background: @primary;
  color: white;
  display: flex;
  align-items: center;
  justify-content: center;
  font-weight: bold;
}

.step-title {
  font-size: 18px;
  font-weight: 600;
  color: @dark;
  margin: 0 0 8px 0;
}

.step-description {
  color: @muted;
  line-height: 1.5;
  margin: 0;
}

// 响应式调整
@media (max-width: 768px) {
  .service-process {
    padding: 16px;
  }
}
</style>
