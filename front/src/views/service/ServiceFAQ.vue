<template>
  <div class="faq-section">
    <h3 class="faq-title">常见问题</h3>
    <div class="faq-list">
      <div class="faq-item">
        <button
          class="faq-question"
          @click="toggleFAQ(0)"
        >
          <span>深度保洁和日常保洁有什么区别？</span>
          <ChevronDown
            :size="16"
            class="faq-icon"
            :class="{ 'rotated': faqOpen[0] }"
          />
        </button>
        <div
          class="faq-answer"
          :class="{ 'open': faqOpen[0] }"
        >
          深度保洁是对房屋进行全面、彻底的清洁，包括日常保洁无法涉及的深度清洁项目，如：窗槽清洁、厨房油污深度清洁、卫生间死角清洁、家具内部除尘等。服务时间更长，清洁更彻底。日常保洁主要针对房屋表面的日常清洁维护。
        </div>
      </div>
      <div class="faq-item">
        <button
          class="faq-question"
          @click="toggleFAQ(1)"
        >
          <span>服务人员会带清洁工具和清洁剂吗？</span>
          <ChevronDown
            :size="16"
            class="faq-icon"
            :class="{ 'rotated': faqOpen[1] }"
          />
        </button>
        <div
          class="faq-answer"
          :class="{ 'open': faqOpen[1] }"
        >
          是的，我们的服务人员会携带全套专业清洁工具和环保清洁剂上门服务，您无需准备任何清洁用品。如果您有特殊清洁剂需求，可以提前告知我们。
        </div>
      </div>
      <div class="faq-item">
        <button
          class="faq-question"
          @click="toggleFAQ(2)"
        >
          <span>如何修改或取消预约？</span>
          <ChevronDown
            :size="16"
            class="faq-icon"
            :class="{ 'rotated': faqOpen[2] }"
          />
        </button>
        <div
          class="faq-answer"
          :class="{ 'open': faqOpen[2] }"
        >
          您可以在"我的订单"中找到相应订单，点击"修改预约"或"取消预约"按钮进行操作。服务前24小时可免费取消或修改，24小时内取消将收取30%的服务费用。
        </div>
      </div>
      <div class="faq-item">
        <button
          class="faq-question"
          @click="toggleFAQ(3)"
        >
          <span>服务过程中需要有人在家吗？</span>
          <ChevronDown
            :size="16"
            class="faq-icon"
            :class="{ 'rotated': faqOpen[3] }"
          />
        </button>
        <div
          class="faq-answer"
          :class="{ 'open': faqOpen[3] }"
        >
          建议服务时有成年人在家，以便开门、介绍房屋情况和验收服务质量。如果您无法在家，可以提前与客服沟通，安排钥匙交接事宜。我们所有服务人员都经过严格背景调查，您可以放心。
        </div>
      </div>
      <div class="faq-more">
        <a href="javascript:void(0);" class="more-link">
          查看更多常见问题
          <ChevronRight :size="14" />
        </a>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref } from 'vue'
import {
  ChevronDown,
  ChevronRight
} from 'lucide-vue-next'

// 响应式数据
const faqOpen = ref([false, false, false, false])

// 方法
const toggleFAQ = (index) => {
  faqOpen.value[index] = !faqOpen.value[index]
}
</script>

<style lang="less" scoped>
// 颜色变量
@primary: #165dff;
@dark: #1f2937;
@muted: #6b7280;

.faq-section {
  background: white;
  border-radius: 12px;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
  overflow: hidden;
  padding: 24px;
}

.faq-title {
  font-size: 18px;
  font-weight: bold;
  color: @dark;
  margin: 0 0 16px 0;
}

.faq-list {
  display: flex;
  flex-direction: column;
  gap: 16px;
}

.faq-item {
  border-bottom: 1px solid #f0f0f0;
  padding-bottom: 16px;

  &:last-child {
    border-bottom: none;
    padding-bottom: 0;
  }
}

.faq-question {
  display: flex;
  justify-content: space-between;
  align-items: center;
  width: 100%;
  text-align: left;
  font-size: 14px;
  font-weight: 500;
  background: none;
  border: none;
  cursor: pointer;
  padding: 0;
  color: @dark;

  &:hover {
    color: @primary;
  }
}

.faq-icon {
  transition: transform 0.3s ease;
  color: @muted;

  &.rotated {
    transform: rotate(180deg);
  }
}

.faq-answer {
  margin-top: 8px;
  font-size: 12px;
  color: @muted;
  line-height: 1.5;
  max-height: 0;
  overflow: hidden;
  transition: max-height 0.3s ease;

  &.open {
    max-height: 200px;
  }
}

.faq-more {
  text-align: center;
  margin-top: 8px;
}

.more-link {
  color: @primary;
  font-size: 14px;
  text-decoration: none;
  display: inline-flex;
  align-items: center;
  gap: 4px;

  &:hover {
    text-decoration: underline;
  }
}

// 响应式调整
@media (max-width: 768px) {
  .faq-section {
    padding: 16px;
  }
}
</style>
