<template>
  <div class="contact-service">
    <h3 class="contact-title">有疑问？联系客服</h3>
    <p class="contact-subtitle">
      我们的客服团队将为您提供专业解答
    </p>
    <t-button
      theme="primary"
      size="large"
      block
      @click="handleContact"
      class="contact-btn"
    >
      <template #icon>
        <MessageCircle :size="18" />
      </template>
      在线咨询
    </t-button>
    <div class="contact-methods">
      <a href="javascript:void(0);" class="contact-method">
        <MessageSquare :size="18" />
      </a>
      <a href="javascript:void(0);" class="contact-method">
        <Phone :size="18" />
      </a>
      <a href="javascript:void(0);" class="contact-method">
        <Mail :size="18" />
      </a>
    </div>
    <div class="contact-info">
      客服热线：************<br />
      服务时间：7:00-23:00
    </div>
  </div>
</template>

<script setup>
import {
  MessageCircle,
  MessageSquare,
  Phone,
  Mail
} from 'lucide-vue-next'

// 方法
const handleContact = () => {
  console.log('联系客服')
  // 这里可以添加客服联系逻辑
}
</script>

<style lang="less" scoped>
// 颜色变量
@primary: #165dff;

.contact-service {
  background: @primary;
  color: white;
  border-radius: 12px;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
  overflow: hidden;
  padding: 24px;
}

.contact-title {
  font-size: 18px;
  font-weight: bold;
  color: white;
  margin: 0 0 16px 0;
}

.contact-subtitle {
  color: rgba(255, 255, 255, 0.8);
  font-size: 14px;
  margin: 0 0 16px 0;
}

.contact-btn {
  margin-bottom: 16px;
}

.contact-methods {
  display: flex;
  justify-content: center;
  gap: 16px;
  margin-bottom: 16px;
}

.contact-method {
  background: rgba(255, 255, 255, 0.2);
  padding: 8px;
  border-radius: 50%;
  color: white;
  text-decoration: none;
  transition: background 0.2s;

  &:hover {
    background: rgba(255, 255, 255, 0.3);
  }
}

.contact-info {
  text-align: center;
  font-size: 12px;
  color: rgba(255, 255, 255, 0.8);
  line-height: 1.4;
}

// 响应式调整
@media (max-width: 768px) {
  .contact-service {
    padding: 16px;
  }
}
</style>
