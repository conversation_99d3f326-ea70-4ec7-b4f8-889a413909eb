<template>
  <div class="demo-page">
    <div class="demo-header">
      <h1>相关服务推荐组件演示</h1>
      <div class="demo-controls">
        <t-button @click="toggleEmptyState">
          {{ isEmpty ? '显示服务列表' : '显示空状态' }}
        </t-button>
      </div>
    </div>
    
    <div class="demo-content">
      <RelatedServices :services="currentServices" />
    </div>
  </div>
</template>

<script setup>
import { ref, computed } from 'vue'
import RelatedServices from './RelatedServices.vue'

// 控制是否显示空状态
const isEmpty = ref(true)

// 模拟服务数据
const mockServices = [
  {
    id: 1,
    name: '深度清洁服务',
    image: 'https://via.placeholder.com/300x200?text=深度清洁',
    rating: 4.8,
    price: 199,
    badge: '热门',
    badgeType: 'hot'
  },
  {
    id: 2,
    name: '家电维修服务',
    image: 'https://via.placeholder.com/300x200?text=家电维修',
    rating: 4.6,
    price: 150,
    badge: '优惠',
    badgeType: 'discount'
  },
  {
    id: 3,
    name: '管道疏通服务',
    image: 'https://via.placeholder.com/300x200?text=管道疏通',
    rating: 4.7,
    price: 120
  }
]

// 当前显示的服务列表
const currentServices = computed(() => {
  return isEmpty.value ? [] : mockServices
})

// 切换空状态
const toggleEmptyState = () => {
  isEmpty.value = !isEmpty.value
}
</script>

<style lang="less" scoped>
.demo-page {
  padding: 24px;
  max-width: 1200px;
  margin: 0 auto;
}

.demo-header {
  margin-bottom: 32px;
  text-align: center;
  
  h1 {
    font-size: 24px;
    color: #1f2937;
    margin-bottom: 16px;
  }
}

.demo-controls {
  display: flex;
  justify-content: center;
  gap: 16px;
}

.demo-content {
  background: #f8fafc;
  padding: 24px;
  border-radius: 8px;
}
</style>
