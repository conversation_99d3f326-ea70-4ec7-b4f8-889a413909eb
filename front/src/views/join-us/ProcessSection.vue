<template>
  <section class="process-section">
    <div class="container">
      <div class="section-header">
        <h2 class="section-title">加入流程</h2>
        <p class="section-description">
          简单几步，开启你的家政服务职业生涯
        </p>
      </div>
      <div class="process-container">
        <div class="process-grid">
          <!-- 步骤 1 -->
          <div class="process-step">
            <div class="step-icon">
              <FileText :size="24" />
            </div>
            <h3 class="step-title">提交申请</h3>
            <p class="step-description">填写在线申请表，选择你感兴趣的岗位</p>
          </div>
          <!-- 步骤 2 -->
          <div class="process-step">
            <div class="step-icon">
              <UserCheck :size="24" />
            </div>
            <h3 class="step-title">面试评估</h3>
            <p class="step-description">参加面试和技能评估，了解你的专业能力</p>
          </div>
          <!-- 步骤 3 -->
          <div class="process-step">
            <div class="step-icon">
              <GraduationCap :size="24" />
            </div>
            <h3 class="step-title">专业培训</h3>
            <p class="step-description">参加公司提供的专业技能培训课程</p>
          </div>
          <!-- 步骤 4 -->
          <div class="process-step">
            <div class="step-icon">
              <Briefcase :size="24" />
            </div>
            <h3 class="step-title">正式上岗</h3>
            <p class="step-description">通过考核后正式加入，开始你的职业生涯</p>
          </div>
        </div>
        <!-- 连接线 -->
        <div class="process-line"></div>
      </div>
    </div>
  </section>
</template>

<script setup>
import {
  FileText,
  UserCheck,
  GraduationCap,
  Briefcase
} from 'lucide-vue-next'
</script>

<style lang="less" scoped>
// 颜色变量
@primary: #2563eb;
@secondary: #4f46e5;
@accent: #f97316;
@neutral: #f3f4f6;
@dark: #1e293b;
@gray-50: #f9fafb;
@gray-600: #4b5563;
@gray-700: #374151;
@gray-800: #1f2937;

// 容器
.container {
  max-width: 1200px;
  margin: 0 auto;
  padding: 0 16px;

  @media (min-width: 640px) {
    padding: 0 24px;
  }

  @media (min-width: 1024px) {
    padding: 0 32px;
  }
}

// 通用部分样式
.section-header {
  text-align: center;
  max-width: 768px;
  margin: 0 auto 64px;
}

.section-title {
  font-size: clamp(1.8rem, 4vw, 2.5rem);
  font-weight: bold;
  color: @dark;
  margin-bottom: 16px;
}

.section-description {
  font-size: 18px;
  color: @gray-600;
}

// 流程部分
.process-section {
  padding: 64px 0;
  background-color: @gray-50;

  @media (min-width: 768px) {
    padding: 96px 0;
  }
}

.process-container {
  position: relative;
}

.process-grid {
  display: grid;
  grid-template-columns: 1fr;
  gap: 32px;
  position: relative;
  z-index: 10;

  @media (min-width: 768px) {
    grid-template-columns: repeat(4, 1fr);
  }
}

.process-step {
  text-align: center;
  padding: 24px;
  background-color: white;
  border-radius: 12px;
  box-shadow: 0 10px 15px -3px rgba(0, 0, 0, 0.1);
  transition: all 0.3s ease;

  &:hover {
    transform: translateY(-8px);
    box-shadow: 0 20px 25px -5px rgba(0, 0, 0, 0.1);
  }
}

.step-icon {
  width: 64px;
  height: 64px;
  background-color: @primary;
  color: white;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 24px;
  font-weight: bold;
  margin: 0 auto 16px;
  box-shadow: 0 10px 15px -3px rgba(0, 0, 0, 0.1);

  @media (min-width: 768px) {
    width: 80px;
    height: 80px;
    font-size: 32px;
  }
}

.step-title {
  font-size: 20px;
  font-weight: bold;
  margin-bottom: 8px;
}

.step-description {
  color: @gray-600;
}

.process-line {
  display: none;

  @media (min-width: 768px) {
    display: flex;
    position: absolute;
    top: 50%;
    left: 0;
    width: 100%;
    transform: translateY(-50%);
    z-index: 0;

    &::before {
      content: '';
      flex: 1;
      height: 4px;
      background-color: @primary;
      margin-left: 64px;
    }

    &::after {
      content: '';
      flex: 1;
      height: 4px;
      background-color: @primary;
    }

    & > div:nth-child(2) {
      flex: 1;
      height: 4px;
      background-color: @primary;
    }

    & > div:last-child {
      flex: 1;
      height: 4px;
      background-color: transparent;
      margin-right: 64px;
    }
  }
}

// 响应式调整
@media (max-width: 768px) {
  .process-grid {
    gap: 16px;
  }
}
</style>
