<template>
  <section class="benefits-section" id="benefits" ref="benefitsSection">
    <div class="container">
      <div class="benefits-content">
        <div class="benefits-image">
          <div class="image-container">
            <img
              alt="家政团队合影"
              src="https://design.gemcoder.com/staticResource/echoAiSystemImages/c68a5592921cf8a1b991c053f7d13c7c.png"
            />
          </div>
          <div class="bg-decoration bg-decoration-3"></div>
        </div>
        <div class="benefits-text">
          <h2 class="section-title">我们的员工福利</h2>
          <p class="section-description">
            我们重视每一位团队成员，提供全面的福利保障和良好的工作环境，让你工作安心、生活舒心。
          </p>
          <div class="benefits-list">
            <!-- 福利项 1 -->
            <div class="benefit-item">
              <div class="benefit-icon benefit-icon-primary">
                <CreditCard :size="20" />
              </div>
              <div class="benefit-content">
                <h3 class="benefit-title">有竞争力的薪酬</h3>
                <p class="benefit-description">
                  高于行业平均水平的薪资，绩效奖金，年终分红，定期调薪机制。
                </p>
              </div>
            </div>
            <!-- 福利项 2 -->
            <div class="benefit-item">
              <div class="benefit-icon benefit-icon-secondary">
                <Heart :size="20" />
              </div>
              <div class="benefit-content">
                <h3 class="benefit-title">完善的社会保障</h3>
                <p class="benefit-description">
                  五险一金，商业意外险，年度体检，生育补贴，带薪病假。
                </p>
              </div>
            </div>
            <!-- 福利项 3 -->
            <div class="benefit-item">
              <div class="benefit-icon benefit-icon-accent">
                <GraduationCap :size="20" />
              </div>
              <div class="benefit-content">
                <h3 class="benefit-title">专业培训发展</h3>
                <p class="benefit-description">
                  免费岗前培训，在职技能提升，职业资格认证补贴，管理培训计划。
                </p>
              </div>
            </div>
            <!-- 福利项 4 -->
            <div class="benefit-item">
              <div class="benefit-icon benefit-icon-green">
                <Gift :size="20" />
              </div>
              <div class="benefit-content">
                <h3 class="benefit-title">丰富的员工活动</h3>
                <p class="benefit-description">
                  节日福利，生日惊喜，团队建设活动，年度旅游，优秀员工表彰。
                </p>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </section>
</template>

<script setup>
import { ref } from 'vue'
import {
  CreditCard,
  Heart,
  GraduationCap,
  Gift
} from 'lucide-vue-next'

// 模板引用
const benefitsSection = ref(null)

// 暴露给父组件
defineExpose({
  benefitsSection
})
</script>

<style lang="less" scoped>
// 颜色变量
@primary: #2563eb;
@secondary: #4f46e5;
@accent: #f97316;
@neutral: #f3f4f6;
@dark: #1e293b;
@gray-50: #f9fafb;
@gray-600: #4b5563;
@gray-700: #374151;
@gray-800: #1f2937;

// 容器
.container {
  max-width: 1200px;
  margin: 0 auto;
  padding: 0 16px;

  @media (min-width: 640px) {
    padding: 0 24px;
  }

  @media (min-width: 1024px) {
    padding: 0 32px;
  }
}

// 通用部分样式
.section-title {
  font-size: clamp(1.8rem, 4vw, 2.5rem);
  font-weight: bold;
  color: @dark;
  margin-bottom: 16px;
}

.section-description {
  font-size: 18px;
  color: @gray-600;
}

// 福利部分
.benefits-section {
  padding: 64px 0;
  background-color: white;

  @media (min-width: 768px) {
    padding: 96px 0;
  }
}

.benefits-content {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 48px;

  @media (min-width: 1024px) {
    flex-direction: row;
  }
}

.benefits-image {
  width: 100%;
  position: relative;

  @media (min-width: 1024px) {
    width: 50%;
  }

  .image-container {
    position: relative;
    z-index: 10;
    border-radius: 16px;
    overflow: hidden;
    box-shadow: 0 25px 50px -12px rgba(0, 0, 0, 0.25);

    img {
      width: 100%;
      height: auto;
      object-fit: cover;
    }
  }
}

.bg-decoration-3 {
  position: absolute;
  bottom: -24px;
  right: -24px;
  width: 160px;
  height: 160px;
  background-color: fade(@primary, 10%);
  border-radius: 50%;
  filter: blur(48px);
  z-index: 0;
}

.benefits-text {
  width: 100%;

  @media (min-width: 1024px) {
    width: 50%;
  }

  .section-title {
    text-align: left;
    margin-bottom: 24px;
  }

  .section-description {
    text-align: left;
    margin-bottom: 32px;
  }
}

.benefits-list {
  display: flex;
  flex-direction: column;
  gap: 24px;
}

.benefit-item {
  display: flex;
  align-items: flex-start;
}

.benefit-icon {
  flex-shrink: 0;
  width: 48px;
  height: 48px;
  border-radius: 8px;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-right: 16px;
  font-size: 20px;

  &.benefit-icon-primary {
    background-color: fade(@primary, 10%);
    color: @primary;
  }

  &.benefit-icon-secondary {
    background-color: fade(@secondary, 10%);
    color: @secondary;
  }

  &.benefit-icon-accent {
    background-color: fade(@accent, 10%);
    color: @accent;
  }

  &.benefit-icon-green {
    background-color: fade(#10b981, 10%);
    color: #10b981;
  }
}

.benefit-content {
  flex: 1;
}

.benefit-title {
  font-size: 20px;
  font-weight: bold;
  margin-bottom: 4px;
}

.benefit-description {
  color: @gray-600;
}
</style>
