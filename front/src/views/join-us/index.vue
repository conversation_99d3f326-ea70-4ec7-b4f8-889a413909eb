<template>
  <div class="join-us-page">
    <!-- 全屏加载动画 -->
    <div v-if="loading" class="loading-overlay">
      <div class="loading-container">
        <div class="loading-spinner">
          <div class="spinner-ring"></div>
          <div class="spinner-ring"></div>
          <div class="spinner-ring"></div>
        </div>
        <div class="loading-text">{{ loadingText }}</div>
        <div class="loading-dots">
          <span class="dot"></span>
          <span class="dot"></span>
          <span class="dot"></span>
        </div>
      </div>
    </div>

    <!-- 英雄区域 -->
    <HeroSection
      @scroll-to-form="scrollToForm"
      @scroll-to-benefits="scrollToBenefits"
    />

    <!-- 我们的优势 -->
    <AdvantagesSection />

    <!-- 员工福利 -->
    <BenefitsSection ref="benefitsSectionRef" />

    <!-- 加入流程 -->
    <ProcessSection />

    <!-- 申请表单 -->
    <ApplicationForm ref="formSectionRef" />

    <!-- 常见问题 -->
    <FAQSection />

    <!-- 返回顶部按钮 -->
    <button
      v-show="showBackToTop"
      @click="scrollToTop"
      class="back-to-top"
    >
      <ChevronUp :size="20" />
    </button>
  </div>
</template>

<script setup>
import { ref, onMounted, onUnmounted } from 'vue'
import { ChevronUp } from 'lucide-vue-next'

// 导入子组件
import HeroSection from './HeroSection.vue'
import AdvantagesSection from './AdvantagesSection.vue'
import BenefitsSection from './BenefitsSection.vue'
import ProcessSection from './ProcessSection.vue'
import ApplicationForm from './ApplicationForm.vue'
import FAQSection from './FAQSection.vue'

// 响应式数据
const showBackToTop = ref(false)
const loading = ref(true)
const loadingText = ref('正在加载页面内容...')

// 子组件引用
const benefitsSectionRef = ref(null)
const formSectionRef = ref(null)

// 滚动到表单
const scrollToForm = () => {
  if (formSectionRef.value?.formSection) {
    formSectionRef.value.formSection.scrollIntoView({ behavior: 'smooth', block: 'start' })
  }
}

// 滚动到福利部分
const scrollToBenefits = () => {
  if (benefitsSectionRef.value?.benefitsSection) {
    benefitsSectionRef.value.benefitsSection.scrollIntoView({ behavior: 'smooth', block: 'start' })
  }
}

// 滚动到顶部
const scrollToTop = () => {
  window.scrollTo({
    top: 0,
    behavior: 'smooth'
  })
}

// 处理滚动事件
const handleScroll = () => {
  showBackToTop.value = window.scrollY > 300
}

// 模拟页面加载
const loadPageContent = async () => {
  try {
    // 模拟加载延迟
    await new Promise(resolve => setTimeout(resolve, 1500))
    loading.value = false
  } catch (error) {
    console.error('页面加载失败:', error)
    loading.value = false
  }
}

// 生命周期
onMounted(() => {
  window.addEventListener('scroll', handleScroll)
  loadPageContent()
})

onUnmounted(() => {
  window.removeEventListener('scroll', handleScroll)
})
</script>

<style lang="less" scoped>
// 颜色变量
@primary: #2563eb;
@dark: #1e293b;
@gray-50: #f9fafb;
@gray-800: #1f2937;

// 基础样式
.join-us-page {
  font-family: 'Inter', system-ui, sans-serif;
  background-color: @gray-50;
  color: @gray-800;
  min-height: 100vh;
  width: 100%;

  * {
    box-sizing: border-box;
  }
}

// 全屏加载动画
.loading-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: linear-gradient(135deg, rgba(37, 99, 235, 0.1) 0%, rgba(79, 70, 229, 0.1) 100%);
  backdrop-filter: blur(10px);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 9999;
  animation: overlayFadeIn 0.4s ease-out;
}

.loading-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 24px;
  animation: containerSlideIn 0.6s ease-out;
}

// 自定义旋转加载器
.loading-spinner {
  position: relative;
  width: 80px;
  height: 80px;
}

.spinner-ring {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  border: 3px solid transparent;
  border-radius: 50%;
  animation: spin 2s linear infinite;

  &:nth-child(1) {
    border-top-color: @primary;
    animation-duration: 2s;
  }

  &:nth-child(2) {
    border-right-color: #3b82f6;
    animation-duration: 1.5s;
    animation-direction: reverse;
    width: 90%;
    height: 90%;
    top: 5%;
    left: 5%;
  }

  &:nth-child(3) {
    border-bottom-color: #60a5fa;
    animation-duration: 1s;
    width: 80%;
    height: 80%;
    top: 10%;
    left: 10%;
  }
}

// 加载文字
.loading-text {
  font-size: 18px;
  font-weight: 600;
  color: @dark;
  text-align: center;
  letter-spacing: 0.5px;
  margin: 0;
  text-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

// 跳动的点
.loading-dots {
  display: flex;
  gap: 8px;
  align-items: center;
}

.dot {
  width: 8px;
  height: 8px;
  background: @primary;
  border-radius: 50%;
  animation: dotBounce 1.4s ease-in-out infinite both;

  &:nth-child(1) {
    animation-delay: -0.32s;
  }

  &:nth-child(2) {
    animation-delay: -0.16s;
  }

  &:nth-child(3) {
    animation-delay: 0s;
  }
}

// 返回顶部按钮
.back-to-top {
  position: fixed;
  bottom: 24px;
  right: 24px;
  width: 48px;
  height: 48px;
  background-color: @primary;
  color: white;
  border: none;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  box-shadow: 0 10px 15px -3px rgba(0, 0, 0, 0.1);
  transition: all 0.3s ease;
  z-index: 50;
  cursor: pointer;

  &:hover {
    background-color: fade(@primary, 90%);
  }
}

// 加载动画关键帧
@keyframes overlayFadeIn {
  from {
    opacity: 0;
  }
  to {
    opacity: 1;
  }
}

@keyframes containerSlideIn {
  from {
    opacity: 0;
    transform: translateY(30px) scale(0.9);
  }
  to {
    opacity: 1;
    transform: translateY(0) scale(1);
  }
}

@keyframes spin {
  from {
    transform: rotate(0deg);
  }
  to {
    transform: rotate(360deg);
  }
}

@keyframes dotBounce {
  0%, 80%, 100% {
    transform: scale(0.8);
    opacity: 0.5;
  }
  40% {
    transform: scale(1.2);
    opacity: 1;
  }
}

// 响应式调整
@media (max-width: 768px) {
  // 移动端加载动画调整
  .loading-spinner {
    width: 60px;
    height: 60px;
  }

  .loading-text {
    font-size: 16px;
  }

  .dot {
    width: 6px;
    height: 6px;
  }
}
</style>
