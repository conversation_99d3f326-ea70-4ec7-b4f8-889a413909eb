<template>
  <section class="form-section" id="join-form" ref="formSection">
    <div class="container">
      <div class="form-container">
        <div class="section-header">
          <h2 class="section-title">加入我们</h2>
          <p class="section-description">填写以下信息，开启你的家政服务职业生涯</p>
        </div>
        <div class="form-wrapper">
          <t-form ref="formRef" :data="formData" :rules="formRules" @submit="handleSubmit">
            <t-row :gutter="[60, 24]">
              <t-col :span="6">
                <t-form-item label="姓名" name="name" required>
                  <t-input v-model="formData.name" placeholder="请输入姓名"></t-input>
                </t-form-item>
              </t-col>
              <t-col :span="6">
                <t-form-item label="性别" name="gender" required>
                  <t-radio-group v-model="formData.gender">
                    <t-radio :value="0">男</t-radio>
                    <t-radio :value="1">女</t-radio>
                  </t-radio-group>
                </t-form-item>
              </t-col>
              <t-col :span="6">
                <t-form-item label="手机号码" name="phone" required>
                  <t-input v-model="formData.phone" placeholder="请输入您的手机号码" type="tel" />
                </t-form-item>
              </t-col>
              <t-col :span="6">
                <t-form-item label="年龄" name="age" required>
                  <t-input-number v-model="formData.age" placeholder="请输入您的年龄" :min="18" :max="65" auto-width />
                </t-form-item>
              </t-col>
              <t-col :span="6">
                <t-form-item label="个人照片" name="image" required>
                  <ImageUpload
                    v-model="formData.image"
                    @change="handleImageChange"
                    :limit="1"
                  />
                </t-form-item>
              </t-col>
              <t-col :span="6">
                <t-form-item label="从业证书" name="cert" >
                  <ImageUpload
                    v-model="formData.cert"
                    :limit="1"
                  />
                </t-form-item>
              </t-col>
              <t-col :span="6">
                <t-form-item label="服务类型" name="category" required>
                  <t-select
                    v-model="formData.category"
                    :options="categoryOptions"
                    placeholder="请选择服务类型"
                    clearable
                    @change="getJobOptions"
                  ></t-select>
                </t-form-item>
              </t-col>
              <t-col :span="6">
                <t-form-item label="服务岗位" name="job" required>
                  <t-select
                    v-model="formData.job"
                    :options="jobOptions"
                    placeholder="请选择岗位"
                    clearable
                  ></t-select>
                </t-form-item>
              </t-col>
              <t-col :span="12">
                <t-form-item label="工作经验" name="experience" required>
                  <t-textarea v-model="formData.experience" placeholder="请简要描述您的相关工作经验" :autosize="{ minRows: 3, maxRows: 10 }" />
                </t-form-item>
              </t-col>
            </t-row>
            <div class="form-agreement">
              <t-form-item name="agreement" required>
                <t-checkbox v-model="formData.agreement">
                  我已阅读并同意
                  <a href="javascript:void(0);" class="agreement-link">《服务协议》</a>
                  和
                  <a href="javascript:void(0);" class="agreement-link">《隐私政策》</a>
                </t-checkbox>
              </t-form-item>
            </div>
            <div class="form-submit">
              <t-button type="submit" theme="primary" size="large" :loading="submitLoading" class="submit-btn">
                提交申请
              </t-button>
              <t-button theme="default" size="large" @click="handleReset" class="reset-btn">
                重置表单
              </t-button>
            </div>
          </t-form>
        </div>
      </div>
    </div>

    <!-- 成功提交弹窗 -->
    <t-dialog v-model:visible="showSuccessModal" header="申请提交成功" :footer="false" width="400px">
      <div class="success-modal">
        <div class="success-icon">
          <Check :size="24" />
        </div>
        <h3 class="success-title">申请提交成功</h3>
        <p class="success-description">感谢您的申请，我们将在3个工作日内与您联系</p>
        <t-button theme="primary" @click="closeSuccessModal"> 确定 </t-button>
      </div>
    </t-dialog>


  </section>
</template>

<script setup>
import { ref, reactive, onMounted, nextTick } from 'vue';
import { MessagePlugin } from 'tdesign-vue-next';
import { Check } from 'lucide-vue-next';
import { GetJobList } from '@/api/job.js';
import { GetCategoryList } from '@/api/category.js';
import ImageUpload from '@/components/ImageUpload/index.vue';
import { ApplyJoin } from '@/api/staff.js'

// 响应式数据
const formRef = ref(null);
const formSection = ref(null);
const uploadRef = ref(null);
const submitLoading = ref(false);
const showSuccessModal = ref(false);
const jobList = ref([]);
const jobOptions = ref([]);
const categoryList = ref([]);
const categoryOptions = ref([]);


onMounted(async () => {
  await getCategoryOptions();
  await getJobOptions();
});

const applyJoin = async (data) => {
  const response = await ApplyJoin(data)
  if(response.code !==200) {
    await MessagePlugin.error(response.msg || '申请失败，请稍后再试！')
  }
  // 显示成功弹窗
  showSuccessModal.value = true;
}

// 加载岗位列表
const getJobOptions = async () => {
  const response = await GetJobList({
    pageNum: 1,
    pageSize: 1000,
    categoryId: formData.value.category === '' ? null : formData.value.category
  });
  if (response.code !== 200) {
    await MessagePlugin.error(response.msg || '加载岗位列表失败');
    return;
  }
  jobList.value = response.rows || [];
  jobOptions.value = jobList.value.map((item) => ({
    label: item.name,
    value: item.jobId,
  }));
};

const getCategoryOptions = async () => {
  const response = await GetCategoryList({
    pageNum: 1,
    pageSize: 1000,
  });
  if (response.code !== 200) {
    await MessagePlugin.error(response.msg || '加载服务列表失败');
    return;
  }
  categoryList.value = response.rows || [];
  categoryOptions.value = categoryList.value.map((item) => ({
    label: item.name,
    value: item.categoryId,
  }));
};


// 表单数据
const formData = ref({
  name: '',
  gender: 0,
  phone: '',
  age: null,
  image: '',
  category: '',
  job: '',
  cert: '',
  experience: '',
  agreement: false
});

// 表单验证规则
const formRules = {
  name: [
    { required: true, message: '请输入姓名', type: 'error' },
    { min: 2, max: 20, message: '姓名长度应在2-20个字符之间', type: 'error' }
  ],
  gender: [
    { required: true, message: '请选择性别', type: 'error' }
  ],
  phone: [
    { required: true, message: '请输入手机号码', type: 'error' },
    { pattern: /^1[3-9]\d{9}$/, message: '请输入正确的手机号码', type: 'error' }
  ],
  age: [
    { required: true, message: '请输入年龄', type: 'error' },
    { validator: (val) => val >= 18 && val <= 65, message: '年龄应在18-65岁之间', type: 'error' }
  ],
  image: [
    { required: true, message: '请上传个人照片', type: 'error' }
  ],
  category: [
    { required: true, message: '请选择服务类型', type: 'error' }
  ],
  job: [
    { required: true, message: '请选择服务岗位', type: 'error' }
  ],
  experience: [
    { required: true, message: '请描述您的工作经验', type: 'error' },
    { min: 10, message: '工作经验描述至少10个字符', type: 'error' }
  ],
  agreement: [
    { validator: (val) => val === true, message: '请同意服务协议和隐私政策', type: 'error' }
  ]
};

const handleImageChange = (file) => {
  console.log("上传的图片",file);
}

// 表单提交
const handleSubmit = async ({ validateResult }) => {
  if (validateResult === true) {
    submitLoading.value = true;
    try {
      // 深拷贝
      const submitData = JSON.parse(JSON.stringify(formData.value))
      // 去除agreement属性
      delete submitData.agreement
      console.log("提交的表单数据为",submitData);
      await applyJoin(submitData)
      // 提交成功后不重置表单，保持用户填写的内容
    } catch (error) {
      await MessagePlugin.error('提交失败，请重试');
    } finally {
      submitLoading.value = false;
    }
  }
};

// 重置表单
const handleReset = async () => {
  formRef.value.reset()
  // // 先清除表单验证状态
  // if (formRef.value) {
  //   formRef.value.clearValidate();
  // }
  //
  // // 等待一个微任务周期
  // await nextTick();
  //
  // // 重置表单数据到初始状态
  // Object.assign(formData.value, {
  //   name: '',
  //   gender: 0,
  //   phone: '',
  //   age: null,
  //   image: '',
  //   category: '',
  //   job: '',
  //   cert: '',
  //   experience: '',
  //   agreement: false
  // });
  //
  // // 清除上传组件
  // if (uploadRef.value) {
  //   uploadRef.value.clearFiles();
  // }
};

// 关闭成功弹窗
const closeSuccessModal = () => {
  showSuccessModal.value = false;
};

// 暴露给父组件
defineExpose({
  formSection,
});
</script>

<style lang="less" scoped>
// 颜色变量
@primary: #2563eb;
@secondary: #4f46e5;
@accent: #f97316;
@neutral: #f3f4f6;
@dark: #1e293b;
@gray-50: #f9fafb;
@gray-600: #4b5563;
@gray-700: #374151;
@gray-800: #1f2937;

// 容器
.container {
  max-width: 1200px;
  margin: 0 auto;
  padding: 0 16px;

  @media (min-width: 640px) {
    padding: 0 24px;
  }

  @media (min-width: 1024px) {
    padding: 0 32px;
  }
}

// 通用部分样式
.section-header {
  text-align: center;
  max-width: 768px;
  margin: 0 auto 64px;
}

.section-title {
  font-size: clamp(1.8rem, 4vw, 2.5rem);
  font-weight: bold;
  color: @dark;
  margin-bottom: 16px;
}

.section-description {
  font-size: 18px;
  color: @gray-600;
}

// 表单部分
.form-section {
  padding: 64px 0;
  background-color: white;

  @media (min-width: 768px) {
    padding: 96px 0;
  }
}

.form-container {
  max-width: 1024px;
  margin: 0 auto;
}

.form-wrapper {
  background-color: @gray-50;
  border-radius: 16px;
  padding: 24px;
  box-shadow: 0 10px 15px -3px rgba(0, 0, 0, 0.1);

  @media (min-width: 768px) {
    padding: 32px;
  }
}

.form-row {
  display: grid;
  grid-template-columns: 1fr;
  gap: 24px;
  margin-bottom: 24px;

  @media (min-width: 768px) {
    grid-template-columns: repeat(2, 1fr);
  }
}

.form-full {
  margin-bottom: 24px;
}

.certificates-grid {
  display: grid;
  grid-template-columns: 1fr;
  gap: 16px;
  margin-top: 24px;

  @media (min-width: 640px) {
    grid-template-columns: repeat(2, 1fr);
    gap: 20px;
  }

  @media (min-width: 768px) {
    grid-template-columns: repeat(3, 1fr);
    gap: 24px;
  }
}

.form-agreement {
  margin-bottom: 32px;
  margin-top: 13px;
  .agreement-link {
    color: @primary;
    text-decoration: none;

    &:hover {
      text-decoration: underline;
    }
  }
}

.form-submit {
  text-align: center;
  display: flex;
  gap: 16px;
  justify-content: center;
  flex-wrap: wrap;

  .submit-btn, .reset-btn {
    padding: 12px 32px;
    min-width: 120px;

    @media (max-width: 767px) {
      width: 100%;
      margin-bottom: 8px;
    }
  }

  .reset-btn {
    margin-left: 0;
  }
}

// 成功弹窗
.success-modal {
  text-align: center;
  padding: 16px 0;
}

.success-icon {
  width: 64px;
  height: 64px;
  background-color: fade(#10b981, 10%);
  color: #10b981;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  margin: 0 auto 16px;
  font-size: 24px;
}

.success-title {
  font-size: 20px;
  font-weight: bold;
  margin-bottom: 8px;
}

.success-description {
  color: @gray-600;
  margin-bottom: 24px;
}


</style>
