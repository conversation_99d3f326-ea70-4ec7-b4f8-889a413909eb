<template>
  <section class="advantages-section">
    <div class="container">
      <div class="section-header">
        <h2 class="section-title">为什么选择加入我们</h2>
        <p class="section-description">
          我们提供完善的培训体系、有竞争力的薪酬福利和广阔的职业发展空间
        </p>
      </div>
      <div class="advantages-grid">
        <!-- 优势卡片 1 -->
        <div class="advantage-card">
          <div class="advantage-icon advantage-icon-primary">
            <GraduationCap :size="24" />
          </div>
          <h3 class="advantage-title">专业培训体系</h3>
          <p class="advantage-description">
            提供系统化的岗前培训和在职技能提升课程，帮助你成为行业专家。
          </p>
        </div>
        <!-- 优势卡片 2 -->
        <div class="advantage-card">
          <div class="advantage-icon advantage-icon-secondary">
            <Wallet :size="24" />
          </div>
          <h3 class="advantage-title">有竞争力的薪酬</h3>
          <p class="advantage-description">
            高于行业平均水平的薪资待遇，以及丰富的绩效奖金和福利体系。
          </p>
        </div>
        <!-- 优势卡片 3 -->
        <div class="advantage-card">
          <div class="advantage-icon advantage-icon-accent">
            <Calendar :size="24" />
          </div>
          <h3 class="advantage-title">灵活工作安排</h3>
          <p class="advantage-description">
            根据你的时间和技能，提供灵活的工作安排，实现工作与生活的平衡。
          </p>
        </div>
        <!-- 优势卡片 4 -->
        <div class="advantage-card">
          <div class="advantage-icon advantage-icon-green">
            <Users :size="24" />
          </div>
          <h3 class="advantage-title">团队支持</h3>
          <p class="advantage-description">
            加入充满活力的团队，获得持续的技术支持和职业发展指导。
          </p>
        </div>
        <!-- 优势卡片 5 -->
        <div class="advantage-card">
          <div class="advantage-icon advantage-icon-purple">
            <Award :size="24" />
          </div>
          <h3 class="advantage-title">职业发展空间</h3>
          <p class="advantage-description">
            清晰的职业晋升通道，支持你从服务人员成长为团队管理者。
          </p>
        </div>
        <!-- 优势卡片 6 -->
        <div class="advantage-card">
          <div class="advantage-icon advantage-icon-red">
            <Shield :size="24" />
          </div>
          <h3 class="advantage-title">安全保障</h3>
          <p class="advantage-description">
            完善的保险保障和安全防护措施，让你工作无忧。
          </p>
        </div>
      </div>
    </div>
  </section>
</template>

<script setup>
import {
  GraduationCap,
  Wallet,
  Calendar,
  Users,
  Award,
  Shield
} from 'lucide-vue-next'
</script>

<style lang="less" scoped>
// 颜色变量
@primary: #2563eb;
@secondary: #4f46e5;
@accent: #f97316;
@neutral: #f3f4f6;
@dark: #1e293b;
@gray-50: #f9fafb;
@gray-600: #4b5563;
@gray-700: #374151;
@gray-800: #1f2937;

// 容器
.container {
  max-width: 1200px;
  margin: 0 auto;
  padding: 0 16px;

  @media (min-width: 640px) {
    padding: 0 24px;
  }

  @media (min-width: 1024px) {
    padding: 0 32px;
  }
}

// 通用部分样式
.section-header {
  text-align: center;
  max-width: 768px;
  margin: 0 auto 64px;
}

.section-title {
  font-size: clamp(1.8rem, 4vw, 2.5rem);
  font-weight: bold;
  color: @dark;
  margin-bottom: 16px;
}

.section-description {
  font-size: 18px;
  color: @gray-600;
}

// 优势部分
.advantages-section {
  padding: 64px 0;
  background-color: white;

  @media (min-width: 768px) {
    padding: 96px 0;
  }
}

.advantages-grid {
  display: grid;
  grid-template-columns: 1fr;
  gap: 32px;

  @media (min-width: 768px) {
    grid-template-columns: repeat(2, 1fr);
  }

  @media (min-width: 1024px) {
    grid-template-columns: repeat(3, 1fr);
  }
}

.advantage-card {
  background-color: @neutral;
  border-radius: 12px;
  padding: 32px;
  box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1);
  transition: all 0.3s ease;

  &:hover {
    transform: translateY(-5px);
    box-shadow: 0 20px 25px -5px rgba(0, 0, 0, 0.1);
  }
}

.advantage-icon {
  width: 56px;
  height: 56px;
  border-radius: 8px;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-bottom: 24px;
  font-size: 24px;

  &.advantage-icon-primary {
    background-color: fade(@primary, 10%);
    color: @primary;
  }

  &.advantage-icon-secondary {
    background-color: fade(@secondary, 10%);
    color: @secondary;
  }

  &.advantage-icon-accent {
    background-color: fade(@accent, 10%);
    color: @accent;
  }

  &.advantage-icon-green {
    background-color: fade(#10b981, 10%);
    color: #10b981;
  }

  &.advantage-icon-purple {
    background-color: fade(#8b5cf6, 10%);
    color: #8b5cf6;
  }

  &.advantage-icon-red {
    background-color: fade(#ef4444, 10%);
    color: #ef4444;
  }
}

.advantage-title {
  font-size: 20px;
  font-weight: bold;
  margin-bottom: 12px;
}

.advantage-description {
  color: @gray-600;
}

// 响应式调整
@media (max-width: 768px) {
  .advantages-grid {
    gap: 16px;
  }
}
</style>
