<html lang="zh-CN">
<head>
  <meta charset="utf-8" />
  <meta content="width=device-width, initial-scale=1.0" name="viewport" />
  <title>加入我们 - 家政服务平台</title>
  <script src="https://res.gemcoder.com/js/reload.js"></script>
  <script src="https://cdn.tailwindcss.com"></script>
  <link
    href="https://cdn.bootcdn.net/ajax/libs/font-awesome/6.4.0/css/all.min.css"
    rel="stylesheet"
  />
  <script>
    tailwind.config = {
      theme: {
        extend: {
          colors: {
            primary: '#2563eb',
            secondary: '#4f46e5',
            accent: '#f97316',
            neutral: '#f3f4f6',
            dark: '#1e293b'
          },
          fontFamily: {
            inter: ['Inter', 'system-ui', 'sans-serif']
          }
        }
      }
    };
  </script>
  <style type="text/tailwindcss">
      @layer utilities {
          .content-auto {
              content-visibility: auto;
          }
          .text-shadow {
              text-shadow: 0 2px 4px rgba(0,0,0,0.1);
          }
          .card-hover {
              transition: all 0.3s ease;
          }
          .card-hover:hover {
              transform: translateY(-5px);
          }
          .bg-gradient-primary {
              background: linear-gradient(135deg, #2563eb 0%, #4f46e5 100%);
          }
      }
  </style>
</head>
<body class="font-inter bg-gray-50 text-gray-800">
<!-- 英雄区域 -->
<section
  class="pt-16 md:pt-24 pb-16 md:pb-24 bg-gradient-to-br from-blue-50 to-indigo-50"
>
  <div class="container mx-auto px-4 sm:px-6 lg:px-8">
    <div class="flex flex-col md:flex-row items-center">
      <div class="md:w-1/2 mb-10 md:mb-0">
        <h1
          class="text-[clamp(2rem,5vw,3.5rem)] font-bold leading-tight text-dark mb-4"
        >
          加入我们，
          <br />
          <span class="text-primary"> 共创美好家政服务 </span>
        </h1>
        <p class="text-lg md:text-xl text-gray-600 mb-8 max-w-lg">
          成为我们专业团队的一员，为千万家庭提供优质服务，实现个人价值与职业发展的双赢。
        </p>
        <div class="flex flex-col sm:flex-row gap-4">
          <a
            class="bg-primary hover:bg-primary/90 text-white px-6 py-3 rounded-lg transition-all shadow-lg hover:shadow-xl text-center font-medium"
            href="#join-form"
          >
            立即申请
          </a>
          <a
            class="bg-white hover:bg-gray-50 text-primary border border-primary px-6 py-3 rounded-lg transition-all shadow-md hover:shadow-lg text-center font-medium"
            href="#benefits"
          >
            了解福利
          </a>
        </div>
      </div>
      <div class="md:w-1/2 relative">
        <div
          class="relative z-10 rounded-2xl overflow-hidden shadow-2xl transform rotate-2 hover:rotate-0 transition-transform duration-500"
        >
          <img
            alt="专业家政服务人员"
            class="w-full h-auto object-cover"
            src="https://design.gemcoder.com/staticResource/echoAiSystemImages/9a5db32a06dfd02a62f4463781750d6d.png"
          />
        </div>
        <div
          class="absolute top-10 -right-10 w-40 h-40 bg-accent/20 rounded-full blur-3xl z-0"
        ></div>
        <div
          class="absolute -bottom-10 -left-10 w-60 h-60 bg-primary/20 rounded-full blur-3xl z-0"
        ></div>
      </div>
    </div>
  </div>
</section>
<!-- 我们的优势 -->
<section class="py-16 md:py-24 bg-white">
  <div class="container mx-auto px-4 sm:px-6 lg:px-8">
    <div class="text-center max-w-3xl mx-auto mb-16">
      <h2 class="text-[clamp(1.8rem,4vw,2.5rem)] font-bold text-dark mb-4">
        为什么选择加入我们
      </h2>
      <p class="text-lg text-gray-600">
        我们提供完善的培训体系、有竞争力的薪酬福利和广阔的职业发展空间
      </p>
    </div>
    <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
      <!-- 优势卡片 1 -->
      <div
        class="bg-neutral rounded-xl p-8 shadow-md hover:shadow-xl transition-all card-hover"
      >
        <div
          class="w-14 h-14 bg-primary/10 rounded-lg flex items-center justify-center mb-6"
        >
          <i class="fas fa-graduation-cap text-primary text-2xl"> </i>
        </div>
        <h3 class="text-xl font-bold mb-3">专业培训体系</h3>
        <p class="text-gray-600">
          提供系统化的岗前培训和在职技能提升课程，帮助你成为行业专家。
        </p>
      </div>
      <!-- 优势卡片 2 -->
      <div
        class="bg-neutral rounded-xl p-8 shadow-md hover:shadow-xl transition-all card-hover"
      >
        <div
          class="w-14 h-14 bg-secondary/10 rounded-lg flex items-center justify-center mb-6"
        >
          <i class="fas fa-wallet text-secondary text-2xl"> </i>
        </div>
        <h3 class="text-xl font-bold mb-3">有竞争力的薪酬</h3>
        <p class="text-gray-600">
          高于行业平均水平的薪资待遇，以及丰富的绩效奖金和福利体系。
        </p>
      </div>
      <!-- 优势卡片 3 -->
      <div
        class="bg-neutral rounded-xl p-8 shadow-md hover:shadow-xl transition-all card-hover"
      >
        <div
          class="w-14 h-14 bg-accent/10 rounded-lg flex items-center justify-center mb-6"
        >
          <i class="fas fa-calendar-alt text-accent text-2xl"> </i>
        </div>
        <h3 class="text-xl font-bold mb-3">灵活工作安排</h3>
        <p class="text-gray-600">
          根据你的时间和技能，提供灵活的工作安排，实现工作与生活的平衡。
        </p>
      </div>
      <!-- 优势卡片 4 -->
      <div
        class="bg-neutral rounded-xl p-8 shadow-md hover:shadow-xl transition-all card-hover"
      >
        <div
          class="w-14 h-14 bg-green-500/10 rounded-lg flex items-center justify-center mb-6"
        >
          <i class="fas fa-users text-green-500 text-2xl"> </i>
        </div>
        <h3 class="text-xl font-bold mb-3">团队支持</h3>
        <p class="text-gray-600">
          加入充满活力的团队，获得持续的技术支持和职业发展指导。
        </p>
      </div>
      <!-- 优势卡片 5 -->
      <div
        class="bg-neutral rounded-xl p-8 shadow-md hover:shadow-xl transition-all card-hover"
      >
        <div
          class="w-14 h-14 bg-purple-500/10 rounded-lg flex items-center justify-center mb-6"
        >
          <i class="fas fa-medal text-purple-500 text-2xl"> </i>
        </div>
        <h3 class="text-xl font-bold mb-3">职业发展空间</h3>
        <p class="text-gray-600">
          清晰的职业晋升通道，支持你从服务人员成长为团队管理者。
        </p>
      </div>
      <!-- 优势卡片 6 -->
      <div
        class="bg-neutral rounded-xl p-8 shadow-md hover:shadow-xl transition-all card-hover"
      >
        <div
          class="w-14 h-14 bg-red-500/10 rounded-lg flex items-center justify-center mb-6"
        >
          <i class="fas fa-shield-alt text-red-500 text-2xl"> </i>
        </div>
        <h3 class="text-xl font-bold mb-3">安全保障</h3>
        <p class="text-gray-600">
          完善的保险保障和安全防护措施，让你工作无忧。
        </p>
      </div>
    </div>
  </div>
</section>
<!-- 员工福利 -->
<section class="py-16 md:py-24 bg-white" id="benefits">
  <div class="container mx-auto px-4 sm:px-6 lg:px-8">
    <div class="flex flex-col lg:flex-row items-center gap-12">
      <div class="lg:w-1/2">
        <div class="relative">
          <div class="relative z-10 rounded-2xl overflow-hidden shadow-2xl">
            <img
              alt="家政团队合影"
              class="w-full h-auto object-cover"
              src="https://design.gemcoder.com/staticResource/echoAiSystemImages/c68a5592921cf8a1b991c053f7d13c7c.png"
            />
          </div>
          <div
            class="absolute -bottom-6 -right-6 w-40 h-40 bg-primary/10 rounded-full blur-3xl z-0"
          ></div>
        </div>
      </div>
      <div class="lg:w-1/2">
        <h2
          class="text-[clamp(1.8rem,4vw,2.5rem)] font-bold text-dark mb-6"
        >
          我们的员工福利
        </h2>
        <p class="text-lg text-gray-600 mb-8">
          我们重视每一位团队成员，提供全面的福利保障和良好的工作环境，让你工作安心、生活舒心。
        </p>
        <div class="space-y-6">
          <!-- 福利项 1 -->
          <div class="flex items-start">
            <div
              class="flex-shrink-0 w-12 h-12 bg-primary/10 rounded-lg flex items-center justify-center mr-4"
            >
              <i class="fas fa-credit-card text-primary text-xl"> </i>
            </div>
            <div>
              <h3 class="text-xl font-bold mb-1">有竞争力的薪酬</h3>
              <p class="text-gray-600">
                高于行业平均水平的薪资，绩效奖金，年终分红，定期调薪机制。
              </p>
            </div>
          </div>
          <!-- 福利项 2 -->
          <div class="flex items-start">
            <div
              class="flex-shrink-0 w-12 h-12 bg-secondary/10 rounded-lg flex items-center justify-center mr-4"
            >
              <i class="fas fa-medkit text-secondary text-xl"> </i>
            </div>
            <div>
              <h3 class="text-xl font-bold mb-1">完善的社会保障</h3>
              <p class="text-gray-600">
                五险一金，商业意外险，年度体检，生育补贴，带薪病假。
              </p>
            </div>
          </div>
          <!-- 福利项 3 -->
          <div class="flex items-start">
            <div
              class="flex-shrink-0 w-12 h-12 bg-accent/10 rounded-lg flex items-center justify-center mr-4"
            >
              <i class="fas fa-graduation-cap text-accent text-xl"> </i>
            </div>
            <div>
              <h3 class="text-xl font-bold mb-1">专业培训发展</h3>
              <p class="text-gray-600">
                免费岗前培训，在职技能提升，职业资格认证补贴，管理培训计划。
              </p>
            </div>
          </div>
          <!-- 福利项 4 -->
          <div class="flex items-start">
            <div
              class="flex-shrink-0 w-12 h-12 bg-green-500/10 rounded-lg flex items-center justify-center mr-4"
            >
              <i class="fas fa-gift text-green-500 text-xl"> </i>
            </div>
            <div>
              <h3 class="text-xl font-bold mb-1">丰富的员工活动</h3>
              <p class="text-gray-600">
                节日福利，生日惊喜，团队建设活动，年度旅游，优秀员工表彰。
              </p>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</section>
<!-- 加入流程 -->
<section class="py-16 md:py-24 bg-gray-50">
  <div class="container mx-auto px-4 sm:px-6 lg:px-8">
    <div class="text-center max-w-3xl mx-auto mb-16">
      <h2 class="text-[clamp(1.8rem,4vw,2.5rem)] font-bold text-dark mb-4">
        加入流程
      </h2>
      <p class="text-lg text-gray-600">
        简单几步，开启你的家政服务职业生涯
      </p>
    </div>
    <div class="relative">
      <div class="grid grid-cols-1 md:grid-cols-4 gap-8 relative z-10">
        <!-- 步骤 1 -->
        <div
          class="text-center p-6 bg-white rounded-xl shadow-lg hover:shadow-xl transition-all transform hover:-translate-y-2"
        >
          <div
            class="w-16 h-16 md:w-20 md:h-20 bg-primary text-white rounded-full flex items-center justify-center text-xl md:text-2xl font-bold mx-auto mb-4 shadow-lg"
          >
            <i class="fas fa-file-alt"> </i>
          </div>
          <h3 class="text-xl font-bold mb-2">提交申请</h3>
          <p class="text-gray-600">填写在线申请表，选择你感兴趣的岗位</p>
        </div>
        <!-- 步骤 2 -->
        <div
          class="text-center p-6 bg-white rounded-xl shadow-lg hover:shadow-xl transition-all transform hover:-translate-y-2"
        >
          <div
            class="w-16 h-16 md:w-20 md:h-20 bg-primary text-white rounded-full flex items-center justify-center text-xl md:text-2xl font-bold mx-auto mb-4 shadow-lg"
          >
            <i class="fas fa-user-check"> </i>
          </div>
          <h3 class="text-xl font-bold mb-2">面试评估</h3>
          <p class="text-gray-600">参加面试和技能评估，了解你的专业能力</p>
        </div>
        <!-- 步骤 3 -->
        <div
          class="text-center p-6 bg-white rounded-xl shadow-lg hover:shadow-xl transition-all transform hover:-translate-y-2"
        >
          <div
            class="w-16 h-16 md:w-20 md:h-20 bg-primary text-white rounded-full flex items-center justify-center text-xl md:text-2xl font-bold mx-auto mb-4 shadow-lg"
          >
            <i class="fas fa-graduation-cap"> </i>
          </div>
          <h3 class="text-xl font-bold mb-2">专业培训</h3>
          <p class="text-gray-600">参加公司提供的专业技能培训课程</p>
        </div>
        <!-- 步骤 4 -->
        <div
          class="text-center p-6 bg-white rounded-xl shadow-lg hover:shadow-xl transition-all transform hover:-translate-y-2"
        >
          <div
            class="w-16 h-16 md:w-20 md:h-20 bg-primary text-white rounded-full flex items-center justify-center text-xl md:text-2xl font-bold mx-auto mb-4 shadow-lg"
          >
            <i class="fas fa-briefcase"> </i>
          </div>
          <h3 class="text-xl font-bold mb-2">正式上岗</h3>
          <p class="text-gray-600">通过考核后正式加入，开始你的职业生涯</p>
        </div>
      </div>
      <!-- 连接线 -->
      <div
        class="hidden md:flex absolute top-1/2 left-0 w-full -translate-y-1/2 z-0"
      >
        <div class="flex-1 h-1 bg-primary ml-16"></div>
        <div class="flex-1 h-1 bg-primary"></div>
        <div class="flex-1 h-1 bg-primary"></div>
        <div class="flex-1 h-1 bg-transparent mr-16"></div>
      </div>
    </div>
  </div>
</section>
<!-- 申请表单 -->
<section class="py-16 md:py-24 bg-white" id="join-form">
  <div class="container mx-auto px-4 sm:px-6 lg:px-8">
    <div class="max-w-4xl mx-auto">
      <div class="text-center mb-12">
        <h2
          class="text-[clamp(1.8rem,4vw,2.5rem)] font-bold text-dark mb-4"
        >
          加入我们
        </h2>
        <p class="text-lg text-gray-600">
          填写以下信息，开启你的家政服务职业生涯
        </p>
      </div>
      <div class="bg-gray-50 rounded-2xl p-6 md:p-8 shadow-lg">
        <form id="application-form">
          <div class="grid grid-cols-1 md:grid-cols-2 gap-6 mb-6">
            <div>
              <label
                class="block text-sm font-medium text-gray-700 mb-1"
                for="name"
              >
                姓名
                <span class="text-red-500"> * </span>
              </label>
              <input
                class="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-primary focus:border-primary transition-colors"
                id="name"
                name="name"
                placeholder="请输入您的姓名"
                required
                type="text"
              />
            </div>
            <div>
              <label
                class="block text-sm font-medium text-gray-700 mb-1"
                for="phone"
              >
                手机号码
                <span class="text-red-500"> * </span>
              </label>
              <input
                class="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-primary focus:border-primary transition-colors"
                id="phone"
                name="phone"
                placeholder="请输入您的手机号码"
                required
                type="tel"
              />
            </div>
          </div>
          <div class="grid grid-cols-1 md:grid-cols-2 gap-6 mb-6">
            <div>
              <label
                class="block text-sm font-medium text-gray-700 mb-1"
                for="age"
              >
                年龄
                <span class="text-red-500"> * </span>
              </label>
              <input
                class="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-primary focus:border-primary transition-colors"
                id="age"
                max="65"
                min="18"
                name="age"
                placeholder="请输入您的年龄"
                required
                type="number"
              />
            </div>
            <div>
              <label
                class="block text-sm font-medium text-gray-700 mb-1"
                for="position"
              >
                应聘岗位
                <span class="text-red-500"> * </span>
              </label>
              <select
                class="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-primary focus:border-primary transition-colors"
                id="position"
                name="position"
                required
              >
                <option value>请选择岗位</option>
                <option value="cleaner">专业保洁师</option>
                <option value="nanny">育儿嫂</option>
                <option value="maternity-nurse">月嫂</option>
                <option value="appliance-cleaner">家电清洗工程师</option>
                <option value="other">其他岗位</option>
              </select>
            </div>
          </div>
          <div class="mb-6">
            <label
              class="block text-sm font-medium text-gray-700 mb-1"
              for="experience"
            >
              工作经验
              <span class="text-red-500"> * </span>
            </label>
            <textarea
              class="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-primary focus:border-primary transition-colors"
              id="experience"
              name="experience"
              placeholder="请简要描述您的相关工作经验"
              required
              rows="3"
            >
                </textarea>
          </div>
          <div class="grid grid-cols-1 md:grid-cols-2 gap-6 mb-6">
            <div>
              <label
                class="block text-sm font-medium text-gray-700 mb-1"
                for="education"
              >
                学历
              </label>
              <select
                class="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-primary focus:border-primary transition-colors"
                id="education"
                name="education"
              >
                <option value="primary">小学及以下</option>
                <option value="junior">初中</option>
                <option selected value="senior">高中/中专</option>
                <option value="college">大专</option>
                <option value="bachelor">本科及以上</option>
              </select>
            </div>
            <div>
              <label
                class="block text-sm font-medium text-gray-700 mb-1"
                for="address"
              >
                居住地址
              </label>
              <input
                class="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-primary focus:border-primary transition-colors"
                id="address"
                name="address"
                placeholder="请输入您的居住地址"
                type="text"
              />
            </div>
          </div>
          <div class="mb-6">
            <label class="block text-sm font-medium text-gray-700 mb-1">
              持有证书（可多选）
            </label>
            <div
              class="grid grid-cols-1 sm:grid-cols-2 md:grid-cols-3 gap-3"
            >
              <label class="flex items-center">
                <input
                  class="w-4 h-4 text-primary focus:ring-primary border-gray-300 rounded"
                  name="certificates"
                  type="checkbox"
                  value="health"
                />
                <span class="ml-2 text-gray-700"> 健康证 </span>
              </label>
              <label class="flex items-center">
                <input
                  class="w-4 h-4 text-primary focus:ring-primary border-gray-300 rounded"
                  name="certificates"
                  type="checkbox"
                  value="nursing"
                />
                <span class="ml-2 text-gray-700"> 育婴师证 </span>
              </label>
              <label class="flex items-center">
                <input
                  class="w-4 h-4 text-primary focus:ring-primary border-gray-300 rounded"
                  name="certificates"
                  type="checkbox"
                  value="midwife"
                />
                <span class="ml-2 text-gray-700"> 月嫂证 </span>
              </label>
              <label class="flex items-center">
                <input
                  class="w-4 h-4 text-primary focus:ring-primary border-gray-300 rounded"
                  name="certificates"
                  type="checkbox"
                  value="cooking"
                />
                <span class="ml-2 text-gray-700"> 厨师证 </span>
              </label>
              <label class="flex items-center">
                <input
                  class="w-4 h-4 text-primary focus:ring-primary border-gray-300 rounded"
                  name="certificates"
                  type="checkbox"
                  value="first-aid"
                />
                <span class="ml-2 text-gray-700"> 急救证 </span>
              </label>
              <label class="flex items-center">
                <input
                  class="w-4 h-4 text-primary focus:ring-primary border-gray-300 rounded"
                  name="certificates"
                  type="checkbox"
                  value="other"
                />
                <span class="ml-2 text-gray-700"> 其他证书 </span>
              </label>
            </div>
          </div>
          <div class="mb-8">
            <label class="flex items-center">
              <input
                class="w-4 h-4 text-primary focus:ring-primary border-gray-300 rounded"
                required
                type="checkbox"
              />
              <span class="ml-2 text-gray-700 text-sm">
                    我已阅读并同意
                    <a
                      class="text-primary hover:underline"
                      href="javascript:void(0);"
                    >
                      《服务协议》
                    </a>
                    和
                    <a
                      class="text-primary hover:underline"
                      href="javascript:void(0);"
                    >
                      《隐私政策》
                    </a>
                    <span class="text-red-500"> * </span>
                  </span>
            </label>
          </div>
          <div class="text-center">
            <button
              class="bg-primary hover:bg-primary/90 text-white px-8 py-3 rounded-lg transition-all shadow-lg hover:shadow-xl text-center font-medium text-lg w-full md:w-auto"
              type="submit"
            >
              提交申请
            </button>
          </div>
        </form>
      </div>
    </div>
  </div>
</section>
<!-- 常见问题 -->
<section class="py-16 md:py-24 bg-white">
  <div class="container mx-auto px-4 sm:px-6 lg:px-8">
    <div class="text-center max-w-3xl mx-auto mb-16">
      <h2 class="text-[clamp(1.8rem,4vw,2.5rem)] font-bold text-dark mb-4">
        常见问题
      </h2>
      <p class="text-lg text-gray-600">关于加入我们的常见问题解答</p>
    </div>
    <div class="max-w-3xl mx-auto">
      <div class="space-y-4" id="faq-container">
        <!-- FAQ 项 1 -->
        <div class="border border-gray-200 rounded-lg overflow-hidden">
          <button
            class="faq-toggle w-full flex justify-between items-center p-5 text-left bg-white hover:bg-gray-50 transition-colors"
            data-target="faq-1"
          >
                <span class="text-lg font-medium text-gray-800">
                  加入公司需要什么条件？
                </span>
            <i
              class="fas fa-chevron-down text-gray-400 transition-transform"
            >
            </i>
          </button>
          <div
            class="faq-content hidden px-5 pb-5 text-gray-600"
            id="faq-1"
          >
            <p>
              我们对不同岗位有不同的要求，一般要求年龄在18-55岁之间，身体健康，无不良记录，有责任心和良好的服务意识。部分专业岗位如月嫂、育儿嫂等需要相关经验或证书，没有经验的人员我们会提供专业培训。
            </p>
          </div>
        </div>
        <!-- FAQ 项 2 -->
        <div class="border border-gray-200 rounded-lg overflow-hidden">
          <button
            class="faq-toggle w-full flex justify-between items-center p-5 text-left bg-white hover:bg-gray-50 transition-colors"
            data-target="faq-2"
          >
                <span class="text-lg font-medium text-gray-800">
                  培训是免费的吗？需要多长时间？
                </span>
            <i
              class="fas fa-chevron-down text-gray-400 transition-transform"
            >
            </i>
          </button>
          <div
            class="faq-content hidden px-5 pb-5 text-gray-600"
            id="faq-2"
          >
            <p>
              是的，我们提供的所有岗前培训和在职技能提升培训都是免费的。培训时间根据岗位不同有所差异，一般为3-15天不等。培训期间我们会提供一定的生活补贴。
            </p>
          </div>
        </div>
        <!-- FAQ 项 3 -->
        <div class="border border-gray-200 rounded-lg overflow-hidden">
          <button
            class="faq-toggle w-full flex justify-between items-center p-5 text-left bg-white hover:bg-gray-50 transition-colors"
            data-target="faq-3"
          >
                <span class="text-lg font-medium text-gray-800">
                  工作时间是怎样安排的？
                </span>
            <i
              class="fas fa-chevron-down text-gray-400 transition-transform"
            >
            </i>
          </button>
          <div
            class="faq-content hidden px-5 pb-5 text-gray-600"
            id="faq-3"
          >
            <p>
              我们提供灵活的工作安排，包括全职和兼职岗位。全职岗位一般为8小时工作制，兼职岗位可以根据你的时间灵活安排。具体工作时间会根据客户需求和你的个人情况进行匹配。
            </p>
          </div>
        </div>
        <!-- FAQ 项 4 -->
        <div class="border border-gray-200 rounded-lg overflow-hidden">
          <button
            class="faq-toggle w-full flex justify-between items-center p-5 text-left bg-white hover:bg-gray-50 transition-colors"
            data-target="faq-4"
          >
                <span class="text-lg font-medium text-gray-800">
                  薪资待遇如何计算？
                </span>
            <i
              class="fas fa-chevron-down text-gray-400 transition-transform"
            >
            </i>
          </button>
          <div
            class="faq-content hidden px-5 pb-5 text-gray-600"
            id="faq-4"
          >
            <p>
              我们的薪资结构包括基本工资+绩效奖金+服务提成，具体数额根据岗位和技能水平有所不同。一般来说，初级岗位月薪在5000-8000元，有经验的专业岗位月薪可达8000-20000元。我们会为正式员工缴纳五险一金，并提供额外的商业保险。
            </p>
          </div>
        </div>
        <!-- FAQ 项 5 -->
        <div class="border border-gray-200 rounded-lg overflow-hidden">
          <button
            class="faq-toggle w-full flex justify-between items-center p-5 text-left bg-white hover:bg-gray-50 transition-colors"
            data-target="faq-5"
          >
                <span class="text-lg font-medium text-gray-800">
                  公司会提供工作设备和工具吗？
                </span>
            <i
              class="fas fa-chevron-down text-gray-400 transition-transform"
            >
            </i>
          </button>
          <div
            class="faq-content hidden px-5 pb-5 text-gray-600"
            id="faq-5"
          >
            <p>
              是的，公司会为员工提供统一的工作制服和专业的清洁工具、设备及清洁剂。所有工具和材料都符合环保和安全标准，确保服务质量和员工安全。
            </p>
          </div>
        </div>
      </div>
    </div>
  </div>
</section>
<!-- 返回顶部按钮 -->
<button
  class="fixed bottom-6 right-6 w-12 h-12 bg-primary text-white rounded-full flex items-center justify-center shadow-lg opacity-0 invisible transition-all duration-300 hover:bg-primary/90"
  id="back-to-top"
>
  <i class="fas fa-chevron-up"> </i>
</button>
<!-- 成功提交弹窗 -->
<div
  class="fixed inset-0 bg-black/50 flex items-center justify-center z-50 hidden"
  id="success-modal"
>
  <div
    class="bg-white rounded-xl p-8 max-w-md w-full mx-4 transform transition-all"
  >
    <div class="text-center">
      <div
        class="w-16 h-16 bg-green-100 text-green-500 rounded-full flex items-center justify-center mx-auto mb-4"
      >
        <i class="fas fa-check text-2xl"> </i>
      </div>
      <h3 class="text-xl font-bold mb-2">申请提交成功</h3>
      <p class="text-gray-600 mb-6">
        感谢您的申请，我们将在3个工作日内与您联系
      </p>
      <button
        class="bg-primary hover:bg-primary/90 text-white px-6 py-3 rounded-lg transition-all shadow-md hover:shadow-lg font-medium"
        id="close-modal"
      >
        确定
      </button>
    </div>
  </div>
</div>
<script>
  // 页面加载时初始化
  document.addEventListener('DOMContentLoaded', function () {
    // FAQ 切换
    var faqToggles = document.querySelectorAll('.faq-toggle');
    faqToggles.forEach(function (toggle) {
      toggle.addEventListener('click', function () {
        var targetId = toggle.getAttribute('data-target');
        var targetContent = document.getElementById(targetId);
        var icon = toggle.querySelector('i');

        // 关闭其他所有FAQ
        document.querySelectorAll('.faq-content').forEach(function (content) {
          if (content.id !== targetId && !content.classList.contains('hidden')) {
            content.classList.add('hidden');
            var otherIcon = document.querySelector("[data-target=\"".concat(content.id, "\"] i"));
            otherIcon.classList.remove('rotate-180');
          }
        });

        // 切换当前FAQ
        targetContent.classList.toggle('hidden');
        icon.classList.toggle('rotate-180');
      });
    });

    // 返回顶部按钮
    var backToTopButton = document.getElementById('back-to-top');
    window.addEventListener('scroll', function () {
      if (window.scrollY > 300) {
        backToTopButton.classList.remove('opacity-0', 'invisible');
        backToTopButton.classList.add('opacity-100', 'visible');
      } else {
        backToTopButton.classList.add('opacity-0', 'invisible');
        backToTopButton.classList.remove('opacity-100', 'visible');
      }
    });
    backToTopButton.addEventListener('click', function () {
      window.scrollTo({
        top: 0,
        behavior: 'smooth'
      });
    });

    // 平滑滚动
    document.querySelectorAll('a[href^="#"]').forEach(function (anchor) {
      anchor.addEventListener('click', function (e) {
        e.preventDefault();
        var targetId = this.getAttribute('href');
        if (targetId === '#') return;
        var targetElement = document.querySelector(targetId);
        if (targetElement) {
          // 关闭移动菜单（如果打开）
          if (!mobileMenu.classList.contains('hidden')) {
            mobileMenu.classList.add('hidden');
          }
          window.scrollTo({
            top: targetElement.offsetTop - 80,
            behavior: 'smooth'
          });
        }
      });
    });

    // 表单提交处理
    var applicationForm = document.getElementById('application-form');
    var successModal<html lang="zh-CN">
      <head>
      <meta charset="utf-8" />
      <meta content="width=device-width, initial-scale=1.0" name="viewport" />
      <title>加入我们 - 家政服务平台</title>
    <script src="https://res.gemcoder.com/js/reload.js"></script>
<script src="https://cdn.tailwindcss.com"></script>
<link
  href="https://cdn.bootcdn.net/ajax/libs/font-awesome/6.4.0/css/all.min.css"
  rel="stylesheet"
/>
<script>
  tailwind.config = {
    theme: {
      extend: {
        colors: {
          primary: '#2563eb',
          secondary: '#4f46e5',
          accent: '#f97316',
          neutral: '#f3f4f6',
          dark: '#1e293b'
        },
        fontFamily: {
          inter: ['Inter', 'system-ui', 'sans-serif']
        }
      }
    }
  };
</script>
<style type="text/tailwindcss">
    @layer utilities {
        .content-auto {
            content-visibility: auto;
        }
        .text-shadow {
            text-shadow: 0 2px 4px rgba(0,0,0,0.1);
        }
        .card-hover {
            transition: all 0.3s ease;
        }
        .card-hover:hover {
            transform: translateY(-5px);
        }
        .bg-gradient-primary {
            background: linear-gradient(135deg, #2563eb 0%, #4f46e5 100%);
        }
    }
</style>
</head>
<body class="font-inter bg-gray-50 text-gray-800">
<!-- 英雄区域 -->
<section
  class="pt-16 md:pt-24 pb-16 md:pb-24 bg-gradient-to-br from-blue-50 to-indigo-50"
>
  <div class="container mx-auto px-4 sm:px-6 lg:px-8">
    <div class="flex flex-col md:flex-row items-center">
      <div class="md:w-1/2 mb-10 md:mb-0">
        <h1
          class="text-[clamp(2rem,5vw,3.5rem)] font-bold leading-tight text-dark mb-4"
        >
          加入我们，
          <br />
          <span class="text-primary"> 共创美好家政服务 </span>
        </h1>
        <p class="text-lg md:text-xl text-gray-600 mb-8 max-w-lg">
          成为我们专业团队的一员，为千万家庭提供优质服务，实现个人价值与职业发展的双赢。
        </p>
        <div class="flex flex-col sm:flex-row gap-4">
          <a
            class="bg-primary hover:bg-primary/90 text-white px-6 py-3 rounded-lg transition-all shadow-lg hover:shadow-xl text-center font-medium"
            href="#join-form"
          >
            立即申请
          </a>
          <a
            class="bg-white hover:bg-gray-50 text-primary border border-primary px-6 py-3 rounded-lg transition-all shadow-md hover:shadow-lg text-center font-medium"
            href="#benefits"
          >
            了解福利
          </a>
        </div>
      </div>
      <div class="md:w-1/2 relative">
        <div
          class="relative z-10 rounded-2xl overflow-hidden shadow-2xl transform rotate-2 hover:rotate-0 transition-transform duration-500"
        >
          <img
            alt="专业家政服务人员"
            class="w-full h-auto object-cover"
            src="https://design.gemcoder.com/staticResource/echoAiSystemImages/9a5db32a06dfd02a62f4463781750d6d.png"
          />
        </div>
        <div
          class="absolute top-10 -right-10 w-40 h-40 bg-accent/20 rounded-full blur-3xl z-0"
        ></div>
        <div
          class="absolute -bottom-10 -left-10 w-60 h-60 bg-primary/20 rounded-full blur-3xl z-0"
        ></div>
      </div>
    </div>
  </div>
</section>
<!-- 我们的优势 -->
<section class="py-16 md:py-24 bg-white">
  <div class="container mx-auto px-4 sm:px-6 lg:px-8">
    <div class="text-center max-w-3xl mx-auto mb-16">
      <h2 class="text-[clamp(1.8rem,4vw,2.5rem)] font-bold text-dark mb-4">
        为什么选择加入我们
      </h2>
      <p class="text-lg text-gray-600">
        我们提供完善的培训体系、有竞争力的薪酬福利和广阔的职业发展空间
      </p>
    </div>
    <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
      <!-- 优势卡片 1 -->
      <div
        class="bg-neutral rounded-xl p-8 shadow-md hover:shadow-xl transition-all card-hover"
      >
        <div
          class="w-14 h-14 bg-primary/10 rounded-lg flex items-center justify-center mb-6"
        >
          <i class="fas fa-graduation-cap text-primary text-2xl"> </i>
        </div>
        <h3 class="text-xl font-bold mb-3">专业培训体系</h3>
        <p class="text-gray-600">
          提供系统化的岗前培训和在职技能提升课程，帮助你成为行业专家。
        </p>
      </div>
      <!-- 优势卡片 2 -->
      <div
        class="bg-neutral rounded-xl p-8 shadow-md hover:shadow-xl transition-all card-hover"
      >
        <div
          class="w-14 h-14 bg-secondary/10 rounded-lg flex items-center justify-center mb-6"
        >
          <i class="fas fa-wallet text-secondary text-2xl"> </i>
        </div>
        <h3 class="text-xl font-bold mb-3">有竞争力的薪酬</h3>
        <p class="text-gray-600">
          高于行业平均水平的薪资待遇，以及丰富的绩效奖金和福利体系。
        </p>
      </div>
      <!-- 优势卡片 3 -->
      <div
        class="bg-neutral rounded-xl p-8 shadow-md hover:shadow-xl transition-all card-hover"
      >
        <div
          class="w-14 h-14 bg-accent/10 rounded-lg flex items-center justify-center mb-6"
        >
          <i class="fas fa-calendar-alt text-accent text-2xl"> </i>
        </div>
        <h3 class="text-xl font-bold mb-3">灵活工作安排</h3>
        <p class="text-gray-600">
          根据你的时间和技能，提供灵活的工作安排，实现工作与生活的平衡。
        </p>
      </div>
      <!-- 优势卡片 4 -->
      <div
        class="bg-neutral rounded-xl p-8 shadow-md hover:shadow-xl transition-all card-hover"
      >
        <div
          class="w-14 h-14 bg-green-500/10 rounded-lg flex items-center justify-center mb-6"
        >
          <i class="fas fa-users text-green-500 text-2xl"> </i>
        </div>
        <h3 class="text-xl font-bold mb-3">团队支持</h3>
        <p class="text-gray-600">
          加入充满活力的团队，获得持续的技术支持和职业发展指导。
        </p>
      </div>
      <!-- 优势卡片 5 -->
      <div
        class="bg-neutral rounded-xl p-8 shadow-md hover:shadow-xl transition-all card-hover"
      >
        <div
          class="w-14 h-14 bg-purple-500/10 rounded-lg flex items-center justify-center mb-6"
        >
          <i class="fas fa-medal text-purple-500 text-2xl"> </i>
        </div>
        <h3 class="text-xl font-bold mb-3">职业发展空间</h3>
        <p class="text-gray-600">
          清晰的职业晋升通道，支持你从服务人员成长为团队管理者。
        </p>
      </div>
      <!-- 优势卡片 6 -->
      <div
        class="bg-neutral rounded-xl p-8 shadow-md hover:shadow-xl transition-all card-hover"
      >
        <div
          class="w-14 h-14 bg-red-500/10 rounded-lg flex items-center justify-center mb-6"
        >
          <i class="fas fa-shield-alt text-red-500 text-2xl"> </i>
        </div>
        <h3 class="text-xl font-bold mb-3">安全保障</h3>
        <p class="text-gray-600">
          完善的保险保障和安全防护措施，让你工作无忧。
        </p>
      </div>
    </div>
  </div>
</section>
<!-- 员工福利 -->
<section class="py-16 md:py-24 bg-white" id="benefits">
  <div class="container mx-auto px-4 sm:px-6 lg:px-8">
    <div class="flex flex-col lg:flex-row items-center gap-12">
      <div class="lg:w-1/2">
        <div class="relative">
          <div class="relative z-10 rounded-2xl overflow-hidden shadow-2xl">
            <img
              alt="家政团队合影"
              class="w-full h-auto object-cover"
              src="https://design.gemcoder.com/staticResource/echoAiSystemImages/c68a5592921cf8a1b991c053f7d13c7c.png"
            />
          </div>
          <div
            class="absolute -bottom-6 -right-6 w-40 h-40 bg-primary/10 rounded-full blur-3xl z-0"
          ></div>
        </div>
      </div>
      <div class="lg:w-1/2">
        <h2
          class="text-[clamp(1.8rem,4vw,2.5rem)] font-bold text-dark mb-6"
        >
          我们的员工福利
        </h2>
        <p class="text-lg text-gray-600 mb-8">
          我们重视每一位团队成员，提供全面的福利保障和良好的工作环境，让你工作安心、生活舒心。
        </p>
        <div class="space-y-6">
          <!-- 福利项 1 -->
          <div class="flex items-start">
            <div
              class="flex-shrink-0 w-12 h-12 bg-primary/10 rounded-lg flex items-center justify-center mr-4"
            >
              <i class="fas fa-credit-card text-primary text-xl"> </i>
            </div>
            <div>
              <h3 class="text-xl font-bold mb-1">有竞争力的薪酬</h3>
              <p class="text-gray-600">
                高于行业平均水平的薪资，绩效奖金，年终分红，定期调薪机制。
              </p>
            </div>
          </div>
          <!-- 福利项 2 -->
          <div class="flex items-start">
            <div
              class="flex-shrink-0 w-12 h-12 bg-secondary/10 rounded-lg flex items-center justify-center mr-4"
            >
              <i class="fas fa-medkit text-secondary text-xl"> </i>
            </div>
            <div>
              <h3 class="text-xl font-bold mb-1">完善的社会保障</h3>
              <p class="text-gray-600">
                五险一金，商业意外险，年度体检，生育补贴，带薪病假。
              </p>
            </div>
          </div>
          <!-- 福利项 3 -->
          <div class="flex items-start">
            <div
              class="flex-shrink-0 w-12 h-12 bg-accent/10 rounded-lg flex items-center justify-center mr-4"
            >
              <i class="fas fa-graduation-cap text-accent text-xl"> </i>
            </div>
            <div>
              <h3 class="text-xl font-bold mb-1">专业培训发展</h3>
              <p class="text-gray-600">
                免费岗前培训，在职技能提升，职业资格认证补贴，管理培训计划。
              </p>
            </div>
          </div>
          <!-- 福利项 4 -->
          <div class="flex items-start">
            <div
              class="flex-shrink-0 w-12 h-12 bg-green-500/10 rounded-lg flex items-center justify-center mr-4"
            >
              <i class="fas fa-gift text-green-500 text-xl"> </i>
            </div>
            <div>
              <h3 class="text-xl font-bold mb-1">丰富的员工活动</h3>
              <p class="text-gray-600">
                节日福利，生日惊喜，团队建设活动，年度旅游，优秀员工表彰。
              </p>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</section>
<!-- 加入流程 -->
<section class="py-16 md:py-24 bg-gray-50">
  <div class="container mx-auto px-4 sm:px-6 lg:px-8">
    <div class="text-center max-w-3xl mx-auto mb-16">
      <h2 class="text-[clamp(1.8rem,4vw,2.5rem)] font-bold text-dark mb-4">
        加入流程
      </h2>
      <p class="text-lg text-gray-600">
        简单几步，开启你的家政服务职业生涯
      </p>
    </div>
    <div class="relative">
      <div class="grid grid-cols-1 md:grid-cols-4 gap-8 relative z-10">
        <!-- 步骤 1 -->
        <div
          class="text-center p-6 bg-white rounded-xl shadow-lg hover:shadow-xl transition-all transform hover:-translate-y-2"
        >
          <div
            class="w-16 h-16 md:w-20 md:h-20 bg-primary text-white rounded-full flex items-center justify-center text-xl md:text-2xl font-bold mx-auto mb-4 shadow-lg"
          >
            <i class="fas fa-file-alt"> </i>
          </div>
          <h3 class="text-xl font-bold mb-2">提交申请</h3>
          <p class="text-gray-600">填写在线申请表，选择你感兴趣的岗位</p>
        </div>
        <!-- 步骤 2 -->
        <div
          class="text-center p-6 bg-white rounded-xl shadow-lg hover:shadow-xl transition-all transform hover:-translate-y-2"
        >
          <div
            class="w-16 h-16 md:w-20 md:h-20 bg-primary text-white rounded-full flex items-center justify-center text-xl md:text-2xl font-bold mx-auto mb-4 shadow-lg"
          >
            <i class="fas fa-user-check"> </i>
          </div>
          <h3 class="text-xl font-bold mb-2">面试评估</h3>
          <p class="text-gray-600">参加面试和技能评估，了解你的专业能力</p>
        </div>
        <!-- 步骤 3 -->
        <div
          class="text-center p-6 bg-white rounded-xl shadow-lg hover:shadow-xl transition-all transform hover:-translate-y-2"
        >
          <div
            class="w-16 h-16 md:w-20 md:h-20 bg-primary text-white rounded-full flex items-center justify-center text-xl md:text-2xl font-bold mx-auto mb-4 shadow-lg"
          >
            <i class="fas fa-graduation-cap"> </i>
          </div>
          <h3 class="text-xl font-bold mb-2">专业培训</h3>
          <p class="text-gray-600">参加公司提供的专业技能培训课程</p>
        </div>
        <!-- 步骤 4 -->
        <div
          class="text-center p-6 bg-white rounded-xl shadow-lg hover:shadow-xl transition-all transform hover:-translate-y-2"
        >
          <div
            class="w-16 h-16 md:w-20 md:h-20 bg-primary text-white rounded-full flex items-center justify-center text-xl md:text-2xl font-bold mx-auto mb-4 shadow-lg"
          >
            <i class="fas fa-briefcase"> </i>
          </div>
          <h3 class="text-xl font-bold mb-2">正式上岗</h3>
          <p class="text-gray-600">通过考核后正式加入，开始你的职业生涯</p>
        </div>
      </div>
      <!-- 连接线 -->
      <div
        class="hidden md:flex absolute top-1/2 left-0 w-full -translate-y-1/2 z-0"
      >
        <div class="flex-1 h-1 bg-primary ml-16"></div>
        <div class="flex-1 h-1 bg-primary"></div>
        <div class="flex-1 h-1 bg-primary"></div>
        <div class="flex-1 h-1 bg-transparent mr-16"></div>
      </div>
    </div>
  </div>
</section>
<!-- 申请表单 -->
<section class="py-16 md:py-24 bg-white" id="join-form">
  <div class="container mx-auto px-4 sm:px-6 lg:px-8">
    <div class="max-w-4xl mx-auto">
      <div class="text-center mb-12">
        <h2
          class="text-[clamp(1.8rem,4vw,2.5rem)] font-bold text-dark mb-4"
        >
          加入我们
        </h2>
        <p class="text-lg text-gray-600">
          填写以下信息，开启你的家政服务职业生涯
        </p>
      </div>
      <div class="bg-gray-50 rounded-2xl p-6 md:p-8 shadow-lg">
        <form id="application-form">
          <div class="grid grid-cols-1 md:grid-cols-2 gap-6 mb-6">
            <div>
              <label
                class="block text-sm font-medium text-gray-700 mb-1"
                for="name"
              >
                姓名
                <span class="text-red-500"> * </span>
              </label>
              <input
                class="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-primary focus:border-primary transition-colors"
                id="name"
                name="name"
                placeholder="请输入您的姓名"
                required
                type="text"
              />
            </div>
            <div>
              <label
                class="block text-sm font-medium text-gray-700 mb-1"
                for="phone"
              >
                手机号码
                <span class="text-red-500"> * </span>
              </label>
              <input
                class="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-primary focus:border-primary transition-colors"
                id="phone"
                name="phone"
                placeholder="请输入您的手机号码"
                required
                type="tel"
              />
            </div>
          </div>
          <div class="grid grid-cols-1 md:grid-cols-2 gap-6 mb-6">
            <div>
              <label
                class="block text-sm font-medium text-gray-700 mb-1"
                for="age"
              >
                年龄
                <span class="text-red-500"> * </span>
              </label>
              <input
                class="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-primary focus:border-primary transition-colors"
                id="age"
                max="65"
                min="18"
                name="age"
                placeholder="请输入您的年龄"
                required
                type="number"
              />
            </div>
            <div>
              <label
                class="block text-sm font-medium text-gray-700 mb-1"
                for="position"
              >
                应聘岗位
                <span class="text-red-500"> * </span>
              </label>
              <select
                class="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-primary focus:border-primary transition-colors"
                id="position"
                name="position"
                required
              >
                <option value>请选择岗位</option>
                <option value="cleaner">专业保洁师</option>
                <option value="nanny">育儿嫂</option>
                <option value="maternity-nurse">月嫂</option>
                <option value="appliance-cleaner">家电清洗工程师</option>
                <option value="other">其他岗位</option>
              </select>
            </div>
          </div>
          <div class="mb-6">
            <label
              class="block text-sm font-medium text-gray-700 mb-1"
              for="experience"
            >
              工作经验
              <span class="text-red-500"> * </span>
            </label>
            <textarea
              class="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-primary focus:border-primary transition-colors"
              id="experience"
              name="experience"
              placeholder="请简要描述您的相关工作经验"
              required
              rows="3"
            >
                </textarea>
          </div>
          <div class="grid grid-cols-1 md:grid-cols-2 gap-6 mb-6">
            <div>
              <label
                class="block text-sm font-medium text-gray-700 mb-1"
                for="education"
              >
                学历
              </label>
              <select
                class="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-primary focus:border-primary transition-colors"
                id="education"
                name="education"
              >
                <option value="primary">小学及以下</option>
                <option value="junior">初中</option>
                <option selected value="senior">高中/中专</option>
                <option value="college">大专</option>
                <option value="bachelor">本科及以上</option>
              </select>
            </div>
            <div>
              <label
                class="block text-sm font-medium text-gray-700 mb-1"
                for="address"
              >
                居住地址
              </label>
              <input
                class="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-primary focus:border-primary transition-colors"
                id="address"
                name="address"
                placeholder="请输入您的居住地址"
                type="text"
              />
            </div>
          </div>
          <div class="mb-6">
            <label class="block text-sm font-medium text-gray-700 mb-1">
              持有证书（可多选）
            </label>
            <div
              class="grid grid-cols-1 sm:grid-cols-2 md:grid-cols-3 gap-3"
            >
              <label class="flex items-center">
                <input
                  class="w-4 h-4 text-primary focus:ring-primary border-gray-300 rounded"
                  name="certificates"
                  type="checkbox"
                  value="health"
                />
                <span class="ml-2 text-gray-700"> 健康证 </span>
              </label>
              <label class="flex items-center">
                <input
                  class="w-4 h-4 text-primary focus:ring-primary border-gray-300 rounded"
                  name="certificates"
                  type="checkbox"
                  value="nursing"
                />
                <span class="ml-2 text-gray-700"> 育婴师证 </span>
              </label>
              <label class="flex items-center">
                <input
                  class="w-4 h-4 text-primary focus:ring-primary border-gray-300 rounded"
                  name="certificates"
                  type="checkbox"
                  value="midwife"
                />
                <span class="ml-2 text-gray-700"> 月嫂证 </span>
              </label>
              <label class="flex items-center">
                <input
                  class="w-4 h-4 text-primary focus:ring-primary border-gray-300 rounded"
                  name="certificates"
                  type="checkbox"
                  value="cooking"
                />
                <span class="ml-2 text-gray-700"> 厨师证 </span>
              </label>
              <label class="flex items-center">
                <input
                  class="w-4 h-4 text-primary focus:ring-primary border-gray-300 rounded"
                  name="certificates"
                  type="checkbox"
                  value="first-aid"
                />
                <span class="ml-2 text-gray-700"> 急救证 </span>
              </label>
              <label class="flex items-center">
                <input
                  class="w-4 h-4 text-primary focus:ring-primary border-gray-300 rounded"
                  name="certificates"
                  type="checkbox"
                  value="other"
                />
                <span class="ml-2 text-gray-700"> 其他证书 </span>
              </label>
            </div>
          </div>
          <div class="mb-8">
            <label class="flex items-center">
              <input
                class="w-4 h-4 text-primary focus:ring-primary border-gray-300 rounded"
                required
                type="checkbox"
              />
              <span class="ml-2 text-gray-700 text-sm">
                    我已阅读并同意
                    <a
                      class="text-primary hover:underline"
                      href="javascript:void(0);"
                    >
                      《服务协议》
                    </a>
                    和
                    <a
                      class="text-primary hover:underline"
                      href="javascript:void(0);"
                    >
                      《隐私政策》
                    </a>
                    <span class="text-red-500"> * </span>
                  </span>
            </label>
          </div>
          <div class="text-center">
            <button
              class="bg-primary hover:bg-primary/90 text-white px-8 py-3 rounded-lg transition-all shadow-lg hover:shadow-xl text-center font-medium text-lg w-full md:w-auto"
              type="submit"
            >
              提交申请
            </button>
          </div>
        </form>
      </div>
    </div>
  </div>
</section>
<!-- 常见问题 -->
<section class="py-16 md:py-24 bg-white">
  <div class="container mx-auto px-4 sm:px-6 lg:px-8">
    <div class="text-center max-w-3xl mx-auto mb-16">
      <h2 class="text-[clamp(1.8rem,4vw,2.5rem)] font-bold text-dark mb-4">
        常见问题
      </h2>
      <p class="text-lg text-gray-600">关于加入我们的常见问题解答</p>
    </div>
    <div class="max-w-3xl mx-auto">
      <div class="space-y-4" id="faq-container">
        <!-- FAQ 项 1 -->
        <div class="border border-gray-200 rounded-lg overflow-hidden">
          <button
            class="faq-toggle w-full flex justify-between items-center p-5 text-left bg-white hover:bg-gray-50 transition-colors"
            data-target="faq-1"
          >
                <span class="text-lg font-medium text-gray-800">
                  加入公司需要什么条件？
                </span>
            <i
              class="fas fa-chevron-down text-gray-400 transition-transform"
            >
            </i>
          </button>
          <div
            class="faq-content hidden px-5 pb-5 text-gray-600"
            id="faq-1"
          >
            <p>
              我们对不同岗位有不同的要求，一般要求年龄在18-55岁之间，身体健康，无不良记录，有责任心和良好的服务意识。部分专业岗位如月嫂、育儿嫂等需要相关经验或证书，没有经验的人员我们会提供专业培训。
            </p>
          </div>
        </div>
        <!-- FAQ 项 2 -->
        <div class="border border-gray-200 rounded-lg overflow-hidden">
          <button
            class="faq-toggle w-full flex justify-between items-center p-5 text-left bg-white hover:bg-gray-50 transition-colors"
            data-target="faq-2"
          >
                <span class="text-lg font-medium text-gray-800">
                  培训是免费的吗？需要多长时间？
                </span>
            <i
              class="fas fa-chevron-down text-gray-400 transition-transform"
            >
            </i>
          </button>
          <div
            class="faq-content hidden px-5 pb-5 text-gray-600"
            id="faq-2"
          >
            <p>
              是的，我们提供的所有岗前培训和在职技能提升培训都是免费的。培训时间根据岗位不同有所差异，一般为3-15天不等。培训期间我们会提供一定的生活补贴。
            </p>
          </div>
        </div>
        <!-- FAQ 项 3 -->
        <div class="border border-gray-200 rounded-lg overflow-hidden">
          <button
            class="faq-toggle w-full flex justify-between items-center p-5 text-left bg-white hover:bg-gray-50 transition-colors"
            data-target="faq-3"
          >
                <span class="text-lg font-medium text-gray-800">
                  工作时间是怎样安排的？
                </span>
            <i
              class="fas fa-chevron-down text-gray-400 transition-transform"
            >
            </i>
          </button>
          <div
            class="faq-content hidden px-5 pb-5 text-gray-600"
            id="faq-3"
          >
            <p>
              我们提供灵活的工作安排，包括全职和兼职岗位。全职岗位一般为8小时工作制，兼职岗位可以根据你的时间灵活安排。具体工作时间会根据客户需求和你的个人情况进行匹配。
            </p>
          </div>
        </div>
        <!-- FAQ 项 4 -->
        <div class="border border-gray-200 rounded-lg overflow-hidden">
          <button
            class="faq-toggle w-full flex justify-between items-center p-5 text-left bg-white hover:bg-gray-50 transition-colors"
            data-target="faq-4"
          >
                <span class="text-lg font-medium text-gray-800">
                  薪资待遇如何计算？
                </span>
            <i
              class="fas fa-chevron-down text-gray-400 transition-transform"
            >
            </i>
          </button>
          <div
            class="faq-content hidden px-5 pb-5 text-gray-600"
            id="faq-4"
          >
            <p>
              我们的薪资结构包括基本工资+绩效奖金+服务提成，具体数额根据岗位和技能水平有所不同。一般来说，初级岗位月薪在5000-8000元，有经验的专业岗位月薪可达8000-20000元。我们会为正式员工缴纳五险一金，并提供额外的商业保险。
            </p>
          </div>
        </div>
        <!-- FAQ 项 5 -->
        <div class="border border-gray-200 rounded-lg overflow-hidden">
          <button
            class="faq-toggle w-full flex justify-between items-center p-5 text-left bg-white hover:bg-gray-50 transition-colors"
            data-target="faq-5"
          >
                <span class="text-lg font-medium text-gray-800">
                  公司会提供工作设备和工具吗？
                </span>
            <i
              class="fas fa-chevron-down text-gray-400 transition-transform"
            >
            </i>
          </button>
          <div
            class="faq-content hidden px-5 pb-5 text-gray-600"
            id="faq-5"
          >
            <p>
              是的，公司会为员工提供统一的工作制服和专业的清洁工具、设备及清洁剂。所有工具和材料都符合环保和安全标准，确保服务质量和员工安全。
            </p>
          </div>
        </div>
      </div>
    </div>
  </div>
</section>
<!-- 返回顶部按钮 -->
<button
  class="fixed bottom-6 right-6 w-12 h-12 bg-primary text-white rounded-full flex items-center justify-center shadow-lg opacity-0 invisible transition-all duration-300 hover:bg-primary/90"
  id="back-to-top"
>
  <i class="fas fa-chevron-up"> </i>
</button>
<!-- 成功提交弹窗 -->
<div
  class="fixed inset-0 bg-black/50 flex items-center justify-center z-50 hidden"
  id="success-modal"
>
  <div
    class="bg-white rounded-xl p-8 max-w-md w-full mx-4 transform transition-all"
  >
    <div class="text-center">
      <div
        class="w-16 h-16 bg-green-100 text-green-500 rounded-full flex items-center justify-center mx-auto mb-4"
      >
        <i class="fas fa-check text-2xl"> </i>
      </div>
      <h3 class="text-xl font-bold mb-2">申请提交成功</h3>
      <p class="text-gray-600 mb-6">
        感谢您的申请，我们将在3个工作日内与您联系
      </p>
      <button
        class="bg-primary hover:bg-primary/90 text-white px-6 py-3 rounded-lg transition-all shadow-md hover:shadow-lg font-medium"
        id="close-modal"
      >
        确定
      </button>
    </div>
  </div>
</div>
<script>
  // 页面加载时初始化
  document.addEventListener('DOMContentLoaded', function () {
    // FAQ 切换
    var faqToggles = document.querySelectorAll('.faq-toggle');
    faqToggles.forEach(function (toggle) {
      toggle.addEventListener('click', function () {
        var targetId = toggle.getAttribute('data-target');
        var targetContent = document.getElementById(targetId);
        var icon = toggle.querySelector('i');

        // 关闭其他所有FAQ
        document.querySelectorAll('.faq-content').forEach(function (content) {
          if (content.id !== targetId && !content.classList.contains('hidden')) {
            content.classList.add('hidden');
            var otherIcon = document.querySelector("[data-target=\"".concat(content.id, "\"] i"));
            otherIcon.classList.remove('rotate-180');
          }
        });

        // 切换当前FAQ
        targetContent.classList.toggle('hidden');
        icon.classList.toggle('rotate-180');
      });
    });

    // 返回顶部按钮
    var backToTopButton = document.getElementById('back-to-top');
    window.addEventListener('scroll', function () {
      if (window.scrollY > 300) {
        backToTopButton.classList.remove('opacity-0', 'invisible');
        backToTopButton.classList.add('opacity-100', 'visible');
      } else {
        backToTopButton.classList.add('opacity-0', 'invisible');
        backToTopButton.classList.remove('opacity-100', 'visible');
      }
    });
    backToTopButton.addEventListener('click', function () {
      window.scrollTo({
        top: 0,
        behavior: 'smooth'
      });
    });

    // 平滑滚动
    document.querySelectorAll('a[href^="#"]').forEach(function (anchor) {
      anchor.addEventListener('click', function (e) {
        e.preventDefault();
        var targetId = this.getAttribute('href');
        if (targetId === '#') return;
        var targetElement = document.querySelector(targetId);
        if (targetElement) {
          // 关闭移动菜单（如果打开）
          if (!mobileMenu.classList.contains('hidden')) {
            mobileMenu.classList.add('hidden');
          }
          window.scrollTo({
            top: targetElement.offsetTop - 80,
            behavior: 'smooth'
          });
        }
      });
    });

    // 表单提交处理
    var applicationForm = document.getElementById('application-form');
    var successModal = document.getElementById('success-modal');
    var closeModal = document.getElementById('close-modal');
    applicationForm.addEventListener('submit', function (e) {
      e.preventDefault();

      // 这里只是模拟表单提交，实际项目中需要发送到服务器
      // 显示成功弹窗
      successModal.classList.remove('hidden');

      // 重置表单
      applicationForm.reset();
    });
    closeModal.addEventListener('click', function () {
      successModal.classList.add('hidden');
    });

    // 点击弹窗外部关闭
    successModal.addEventListener('click', function (e) {
      if (e.target === successModal && !successModal.classList.contains('hidden')) {
        successModal.classList.add('hidden');
      }
    });
  });
</script>
</body>
</html>
= document.getElementById('success-modal');
    var closeModal = document.getElementById('close-modal');
    applicationForm.addEventListener('submit', function (e) {
      e.preventDefault();

      // 这里只是模拟表单提交，实际项目中需要发送到服务器
      // 显示成功弹窗
      successModal.classList.remove('hidden');

      // 重置表单
      applicationForm.reset();
    });
    closeModal.addEventListener('click', function () {
      successModal.classList.add('hidden');
    });

    // 点击弹窗外部关闭
    successModal.addEventListener('click', function (e) {
      if (e.target === successModal && !successModal.classList.contains('hidden')) {
        successModal.classList.add('hidden');
      }
    });
  });
</script>
</body>
</html>
