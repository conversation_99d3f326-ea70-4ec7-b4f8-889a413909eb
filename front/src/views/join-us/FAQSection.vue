<template>
  <section class="faq-section">
    <div class="container">
      <div class="section-header">
        <h2 class="section-title">常见问题</h2>
        <p class="section-description">关于加入我们的常见问题解答</p>
      </div>
      <div class="faq-container">
        <t-collapse v-model="activeFaq">
          <t-collapse-panel value="faq-1" header="加入公司需要什么条件？">
            <p class="faq-answer">
              我们对不同岗位有不同的要求，一般要求年龄在18-55岁之间，身体健康，无不良记录，有责任心和良好的服务意识。部分专业岗位如月嫂、育儿嫂等需要相关经验或证书，没有经验的人员我们会提供专业培训。
            </p>
          </t-collapse-panel>
          <t-collapse-panel value="faq-2" header="培训是免费的吗？需要多长时间？">
            <p class="faq-answer">
              是的，我们提供的所有岗前培训和在职技能提升培训都是免费的。培训时间根据岗位不同有所差异，一般为3-15天不等。培训期间我们会提供一定的生活补贴。
            </p>
          </t-collapse-panel>
          <t-collapse-panel value="faq-3" header="工作时间是怎样安排的？">
            <p class="faq-answer">
              我们提供灵活的工作安排，包括全职和兼职岗位。全职岗位一般为8小时工作制，兼职岗位可以根据你的时间灵活安排。具体工作时间会根据客户需求和你的个人情况进行匹配。
            </p>
          </t-collapse-panel>
          <t-collapse-panel value="faq-4" header="薪资待遇如何计算？">
            <p class="faq-answer">
              我们的薪资结构包括基本工资+绩效奖金+服务提成，具体数额根据岗位和技能水平有所不同。一般来说，初级岗位月薪在5000-8000元，有经验的专业岗位月薪可达8000-20000元。我们会为正式员工缴纳五险一金，并提供额外的商业保险。
            </p>
          </t-collapse-panel>
          <t-collapse-panel value="faq-5" header="公司会提供工作设备和工具吗？">
            <p class="faq-answer">
              是的，公司会为员工提供统一的工作制服和专业的清洁工具、设备及清洁剂。所有工具和材料都符合环保和安全标准，确保服务质量和员工安全。
            </p>
          </t-collapse-panel>
        </t-collapse>
      </div>
    </div>
  </section>
</template>

<script setup>
import { ref } from 'vue'

const activeFaq = ref([])
</script>

<style lang="less" scoped>
// 颜色变量
@primary: #2563eb;
@secondary: #4f46e5;
@accent: #f97316;
@neutral: #f3f4f6;
@dark: #1e293b;
@gray-50: #f9fafb;
@gray-600: #4b5563;
@gray-700: #374151;
@gray-800: #1f2937;

// 容器
.container {
  max-width: 1200px;
  margin: 0 auto;
  padding: 0 16px;

  @media (min-width: 640px) {
    padding: 0 24px;
  }

  @media (min-width: 1024px) {
    padding: 0 32px;
  }
}

// 通用部分样式
.section-header {
  text-align: center;
  max-width: 768px;
  margin: 0 auto 64px;
}

.section-title {
  font-size: clamp(1.8rem, 4vw, 2.5rem);
  font-weight: bold;
  color: @dark;
  margin-bottom: 16px;
}

.section-description {
  font-size: 18px;
  color: @gray-600;
}

// FAQ部分
.faq-section {
  padding: 64px 0;
  background-color: white;

  @media (min-width: 768px) {
    padding: 96px 0;
  }
}

.faq-container {
  max-width: 768px;
  margin: 0 auto;
}

.faq-answer {
  color: @gray-600;
}

// TDesign 组件样式覆盖
:deep(.t-collapse-panel) {
  border: 1px solid #e5e7eb;
  border-radius: 8px;
  overflow: hidden;
  margin-bottom: 16px;

  .t-collapse-panel__header {
    background-color: white;
    padding: 20px;
    font-weight: 500;
    color: @gray-800;

    &:hover {
      background-color: @gray-50;
    }
  }

  .t-collapse-panel__body {
    padding: 0 20px 20px;
    background-color: white;
  }
}
</style>
