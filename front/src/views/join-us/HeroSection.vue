<template>
  <section class="hero-section">
    <div class="container">
      <div class="hero-content">
        <div class="hero-text">
          <h1 class="hero-title">
            加入我们，
            <br />
            <span class="text-primary">共创美好家政服务</span>
          </h1>
          <p class="hero-description">
            成为我们专业团队的一员，为千万家庭提供优质服务，实现个人价值与职业发展的双赢。
          </p>
          <div class="hero-buttons">
            <a
              class="btn btn-primary"
              href="#join-form"
              @click="scrollToForm"
            >
              立即申请
            </a>
            <a
              class="btn btn-outline"
              href="#benefits"
              @click="scrollToBenefits"
            >
              了解福利
            </a>
          </div>
        </div>
        <div class="hero-image">
          <div class="image-container">
            <img
              alt="专业家政服务人员"
              src="https://design.gemcoder.com/api/searchImage?query=housekeeper%20organizing%20closet%20with%20professional%20organizing%20tools,%20neat%20and%20tidy%20wardrobe,%20minimalist%20style,%20soft%20lighting&width=600&height=400"
            />
          </div>
          <div class="bg-decoration bg-decoration-1"></div>
          <div class="bg-decoration bg-decoration-2"></div>
        </div>
      </div>
    </div>
  </section>
</template>

<script setup>
// 定义事件
const emit = defineEmits(['scroll-to-form', 'scroll-to-benefits'])

// 滚动到表单
const scrollToForm = (e) => {
  e.preventDefault()
  emit('scroll-to-form')
}

// 滚动到福利部分
const scrollToBenefits = (e) => {
  e.preventDefault()
  emit('scroll-to-benefits')
}
</script>

<style lang="less" scoped>
// 颜色变量
@primary: #2563eb;
@secondary: #4f46e5;
@accent: #f97316;
@neutral: #f3f4f6;
@dark: #1e293b;
@gray-50: #f9fafb;
@gray-600: #4b5563;
@gray-700: #374151;
@gray-800: #1f2937;

// 容器
.container {
  max-width: 1200px;
  margin: 0 auto;
  padding: 0 16px;

  @media (min-width: 640px) {
    padding: 0 24px;
  }

  @media (min-width: 1024px) {
    padding: 0 32px;
  }
}

// 英雄区域
.hero-section {
  padding: 64px 0 64px;
  background: linear-gradient(135deg, #dbeafe 0%, #e0e7ff 100%);

  @media (min-width: 768px) {
    padding: 96px 0 96px;
  }
}

.hero-content {
  display: flex;
  flex-direction: column;
  align-items: center;

  @media (min-width: 768px) {
    flex-direction: row;
  }
}

.hero-text {
  width: 100%;
  margin-bottom: 40px;

  @media (min-width: 768px) {
    width: 50%;
    margin-bottom: 0;
  }
}

.hero-title {
  font-size: clamp(2rem, 5vw, 3.5rem);
  font-weight: bold;
  line-height: 1.2;
  color: @dark;
  margin-bottom: 16px;

  .text-primary {
    color: @primary;
  }
}

.hero-description {
  font-size: 18px;
  color: @gray-600;
  margin-bottom: 32px;
  max-width: 500px;

  @media (min-width: 768px) {
    font-size: 20px;
  }
}

.hero-buttons {
  display: flex;
  flex-direction: column;
  gap: 16px;

  @media (min-width: 640px) {
    flex-direction: row;
  }
}

.btn {
  display: inline-block;
  padding: 12px 24px;
  border-radius: 8px;
  text-decoration: none;
  text-align: center;
  font-weight: 500;
  transition: all 0.3s ease;

  &.btn-primary {
    background-color: @primary;
    color: white;
    box-shadow: 0 10px 15px -3px rgba(0, 0, 0, 0.1);

    &:hover {
      background-color: fade(@primary, 90%);
      box-shadow: 0 20px 25px -5px rgba(0, 0, 0, 0.1);
    }
  }

  &.btn-outline {
    background-color: white;
    color: @primary;
    border: 1px solid @primary;
    box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1);

    &:hover {
      background-color: @gray-50;
      box-shadow: 0 10px 15px -3px rgba(0, 0, 0, 0.1);
    }
  }
}

.hero-image {
  width: 100%;
  position: relative;

  @media (min-width: 768px) {
    width: 50%;
  }
}

.image-container {
  position: relative;
  z-index: 10;
  border-radius: 16px;
  overflow: hidden;
  box-shadow: 0 25px 50px -12px rgba(0, 0, 0, 0.25);
  transform: rotate(2deg);
  transition: transform 0.5s ease;

  &:hover {
    transform: rotate(0deg);
  }

  img {
    width: 100%;
    height: auto;
    object-fit: cover;
  }
}

.bg-decoration {
  position: absolute;
  border-radius: 50%;
  filter: blur(48px);
  z-index: 0;

  &.bg-decoration-1 {
    top: 40px;
    right: -40px;
    width: 160px;
    height: 160px;
    background-color: fade(@accent, 20%);
  }

  &.bg-decoration-2 {
    bottom: -40px;
    left: -40px;
    width: 240px;
    height: 240px;
    background-color: fade(@primary, 20%);
  }
}
</style>
