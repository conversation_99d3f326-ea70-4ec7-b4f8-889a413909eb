<template>
  <footer class="footer">
    <div class="footer-container">
      <div class="footer-content">
        <!-- Company Info -->
        <div class="footer-section">
          <div class="company-info">
            <div class="company-logo">
              <div class="logo-icon">
                <t-icon name="home" />
              </div>
              <span class="logo-text">家政帮手</span>
            </div>
            <p class="company-desc">
              专业的家政服务平台，为您提供优质、可靠的家庭服务解决方案。让生活更美好，让家更温馨。
            </p>
            <div class="social-links">
              <a href="#" class="social-link">
                <t-icon name="logo-wechat-stroke" />
              </a>
              <a href="#" class="social-link">
                <t-icon name="logo-qq" />
              </a>
              <a href="#" class="social-link">
                <t-icon name="call" />
              </a>
            </div>
          </div>
        </div>

        <!-- Services -->
        <div class="footer-section">
          <h3 class="section-title">服务项目</h3>
          <ul class="link-list">
            <li><a href="#" class="footer-link">家庭保洁</a></li>
            <li><a href="#" class="footer-link">月嫂服务</a></li>
            <li><a href="#" class="footer-link">育儿嫂</a></li>
            <li><a href="#" class="footer-link">老人护理</a></li>
            <li><a href="#" class="footer-link">钟点工</a></li>
          </ul>
        </div>

        <!-- Quick Links -->
        <div class="footer-section">
          <h3 class="section-title">快速链接</h3>
          <ul class="link-list">
            <li><router-link to="/about" class="footer-link">关于我们</router-link></li>
            <li><router-link to="/contact" class="footer-link">联系我们</router-link></li>
            <li><a href="#" class="footer-link">服务条款</a></li>
            <li><a href="#" class="footer-link">隐私政策</a></li>
            <li><a href="#" class="footer-link">帮助中心</a></li>
          </ul>
        </div>

        <!-- Contact Info -->
        <div class="footer-section">
          <h3 class="section-title">联系方式</h3>
          <div class="contact-list">
            <div class="contact-item">
              <t-icon name="call" class="contact-icon" />
              <span class="contact-text">400-888-8888</span>
            </div>
            <div class="contact-item">
              <t-icon name="mail" class="contact-icon" />
              <span class="contact-text"><EMAIL></span>
            </div>
            <div class="contact-item">
              <t-icon name="location" class="contact-icon" />
              <span class="contact-text">北京市朝阳区建国路88号现代城A座1001室</span>
            </div>
            <div class="contact-item">
              <t-icon name="time" class="contact-icon" />
              <span class="contact-text">服务时间：7:00-22:00</span>
            </div>
          </div>
        </div>
      </div>

      <!-- Bottom Bar -->
      <div class="footer-bottom">
        <p class="copyright">
          © 2024 家政帮手. 保留所有权利.
        </p>
        <div class="bottom-links">
          <a href="#" class="bottom-link">备案号：京ICP备12345678号</a>
        </div>
      </div>
    </div>
  </footer>
</template>

<script setup>
// Footer组件逻辑
</script>

<style scoped lang="less">
.footer {
  background: #262626;
  color: #fff;

  .footer-container {
    max-width: 1200px;
    margin: 0 auto;
    padding: 48px 20px;

    .footer-content {
      display: grid;
      grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
      gap: 32px;

      @media (max-width: 768px) {
        grid-template-columns: 1fr;
        gap: 24px;
      }

      .footer-section {
        .company-info {
          .company-logo {
            display: flex;
            align-items: center;
            margin-bottom: 16px;

            .logo-icon {
              width: 32px;
              height: 32px;
              background: linear-gradient(135deg, #1890ff, #096dd9);
              border-radius: 6px;
              display: flex;
              align-items: center;
              justify-content: center;
              margin-right: 8px;
              color: #fff;
              font-size: 16px;
            }

            .logo-text {
              font-size: 18px;
              font-weight: bold;
            }
          }

          .company-desc {
            color: #bfbfbf;
            font-size: 14px;
            line-height: 1.6;
            margin-bottom: 16px;
          }

          .social-links {
            display: flex;
            gap: 16px;

            .social-link {
              color: #8c8c8c;
              font-size: 20px;
              transition: color 0.3s ease;

              &:hover {
                color: #fff;
              }
            }
          }
        }

        .section-title {
          font-size: 18px;
          font-weight: 600;
          margin-bottom: 16px;
          color: #fff;
        }

        .link-list {
          list-style: none;
          padding: 0;
          margin: 0;

          li {
            margin-bottom: 8px;

            .footer-link {
              color: #bfbfbf;
              text-decoration: none;
              font-size: 14px;
              transition: color 0.3s ease;

              &:hover {
                color: #fff;
              }
            }
          }
        }

        .contact-list {
          .contact-item {
            display: flex;
            align-items: flex-start;
            margin-bottom: 12px;

            .contact-icon {
              color: #1890ff;
              margin-right: 8px;
              margin-top: 2px;
              flex-shrink: 0;
            }

            .contact-text {
              color: #bfbfbf;
              font-size: 14px;
              line-height: 1.4;
            }
          }
        }
      }
    }

    .footer-bottom {
      border-top: 1px solid #434343;
      margin-top: 32px;
      padding-top: 32px;
      display: flex;
      justify-content: space-between;
      align-items: center;

      @media (max-width: 640px) {
        flex-direction: column;
        gap: 16px;
        text-align: center;
      }

      .copyright {
        color: #8c8c8c;
        font-size: 14px;
        margin: 0;
      }

      .bottom-links {
        display: flex;
        gap: 24px;

        .bottom-link {
          color: #8c8c8c;
          text-decoration: none;
          font-size: 14px;
          transition: color 0.3s ease;

          &:hover {
            color: #fff;
          }
        }
      }
    }
  }
}
</style>
