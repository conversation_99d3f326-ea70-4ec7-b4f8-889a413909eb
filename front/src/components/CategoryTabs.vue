<template>
  <div class="category-section">
    <!-- 蓝色背景区域 -->
    <div class="hero-section">
      <div class="hero-content">
        <h1 class="hero-title">专业家政服务，满足您的一切需求</h1>
        <p class="hero-subtitle">我们提供优质家政服务，可靠的家政服务，让您的生活更加轻松便利</p>

        <!-- 搜索框 -->
        <div class="search-container">
          <t-input
            v-model="searchKeyword"
            placeholder="请输入您需要的服务"
            class="search-input"
            size="large"
          >
            <template #suffix-icon>
              <span class="search-icon">🔍</span>
            </template>
          </t-input>
        </div>
      </div>
    </div>

    <!-- 分类标签区域 -->
    <div class="category-tabs">
      <div class="tab-item"
           :class="{ active: activeTag === '全部服务' }"
           @click="selectTag('全部服务')">
        <div class="tab-icon">🏠</div>
        <span class="tab-text">全部服务</span>
      </div>

      <div class="tab-item"
           :class="{ active: activeTag === '清洁服务' }"
           @click="selectTag('清洁服务')">
        <div class="tab-icon">🧹</div>
        <span class="tab-text">清洁服务</span>
      </div>

      <div class="tab-item"
           :class="{ active: activeTag === '母婴护理' }"
           @click="selectTag('母婴护理')">
        <div class="tab-icon">👶</div>
        <span class="tab-text">母婴护理</span>
      </div>

      <div class="tab-item"
           :class="{ active: activeTag === '老人护理' }"
           @click="selectTag('老人护理')">
        <div class="tab-icon">👴</div>
        <span class="tab-text">老人护理</span>
      </div>

      <div class="tab-item"
           :class="{ active: activeTag === '维修服务' }"
           @click="selectTag('维修服务')">
        <div class="tab-icon">🔧</div>
        <span class="tab-text">维修服务</span>
      </div>

      <div class="tab-item"
           :class="{ active: activeTag === '搬家服务' }"
           @click="selectTag('搬家服务')">
        <div class="tab-icon">📦</div>
        <span class="tab-text">搬家服务</span>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref } from 'vue'

// 当前选中的标签
const activeTag = ref('全部服务')

// 搜索关键词
const searchKeyword = ref('')

// 选择标签的方法
const selectTag = (tag) => {
  activeTag.value = tag
  console.log('选中的服务类别:', tag)
  // 这里可以触发事件通知父组件进行筛选
}

// 搜索方法
const handleSearch = () => {
  console.log('搜索关键词:', searchKeyword.value)
  // 这里可以触发搜索事件
}
</script>

<style scoped lang="less">
.category-section {
  width: 100%;
  margin-bottom: 20px;
}

// 蓝色背景区域
.hero-section {
  background: linear-gradient(135deg, #5470c6 0%, #4a5fc1 100%);
  padding: 60px 0 40px 0;
  text-align: center;
  color: white;

  .hero-content {
    max-width: 800px;
    margin: 0 auto;
    padding: 0 20px;
  }

  .hero-title {
    font-size: 32px;
    font-weight: bold;
    margin-bottom: 12px;
    line-height: 1.3;
  }

  .hero-subtitle {
    font-size: 16px;
    margin-bottom: 30px;
    opacity: 0.9;
    line-height: 1.5;
  }

  .search-container {
    max-width: 500px;
    margin: 0 auto;

    .search-input {
      border-radius: 25px;

      :deep(.t-input__inner) {
        border-radius: 25px;
        border: none;
        padding: 12px 20px;
        font-size: 16px;
      }

      .search-icon {
        font-size: 18px;
        cursor: pointer;
      }
    }
  }
}

// 分类标签区域
.category-tabs {
  background: white;
  padding: 20px 0;
  display: flex;
  justify-content: center;
  gap: 40px;
  flex-wrap: wrap;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);

  .tab-item {
    display: flex;
    flex-direction: column;
    align-items: center;
    gap: 8px;
    padding: 15px 20px;
    cursor: pointer;
    transition: all 0.3s ease;
    border-radius: 12px;
    min-width: 80px;

    &:hover {
      background: #f5f7fa;
      transform: translateY(-2px);
    }

    &.active {
      background: #ff6b35;
      color: white;

      .tab-icon {
        color: white;
      }
    }

    .tab-icon {
      font-size: 24px;
      color: #666;
      transition: color 0.3s ease;
    }

    .tab-text {
      font-size: 14px;
      font-weight: 500;
      text-align: center;
    }
  }
}

// 响应式设计
@media (max-width: 768px) {
  .hero-section {
    padding: 40px 0 30px 0;

    .hero-title {
      font-size: 24px;
    }

    .hero-subtitle {
      font-size: 14px;
    }
  }

  .category-tabs {
    gap: 20px;
    padding: 15px 10px;

    .tab-item {
      min-width: 60px;
      padding: 10px 15px;

      .tab-icon {
        font-size: 20px;
      }

      .tab-text {
        font-size: 12px;
      }
    }
  }
}

@media (max-width: 480px) {
  .category-tabs {
    gap: 15px;

    .tab-item {
      min-width: 50px;
      padding: 8px 12px;
    }
  }
}
</style>
