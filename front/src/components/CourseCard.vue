<template>
  <div class="course-card" @click="goToCourseDetail">
    <!-- 课程封面 -->
    <div class="course-cover">
      <img :src="course.courseImage" :alt="course.courseName" class="cover-image" />
    </div>

    <!-- 课程内容 -->
    <div class="course-content">
      <h3 class="course-title">{{ course.courseName }}</h3>
      <p class="course-description">{{ course.description }}</p>

      <!-- 课程标签 -->
      <div class="course-tags">
        <span
          v-for="tag in parsedTags"
          :key="tag"
          class="tag"
        >
          {{ tag }}
        </span>
      </div>
    </div>
  </div>
</template>

<script setup>
import { useRouter } from 'vue-router'
import { computed } from 'vue'

const router = useRouter()

const props = defineProps({
  course: {
    type: Object,
    required: true
  }
})

// 处理标签数据，支持字符串和数组格式
const parsedTags = computed(() => {
  if (!props.course.tag && !props.course.tags) return []

  // 优先使用tag字段（后端返回的字段）
  const tagData = props.course.tag || props.course.tags

  // 如果是字符串，按逗号分割
  if (typeof tagData === 'string') {
    return tagData.split(',').map(tag => tag.trim()).filter(tag => tag)
  }

  // 如果是数组，直接返回
  if (Array.isArray(tagData)) {
    return tagData
  }

  return []
})

// 跳转到课程详情页面
const goToCourseDetail = () => {
  router.push(`/course/${props.course.courseId}`)
}
</script>

<style scoped>
.course-card {
  background: #fff;
  border-radius: 12px;
  overflow: hidden;
  transition: all 0.3s ease;
  cursor: pointer;
  border: 1px solid #f0f0f0;
  display: flex;
  flex-direction: column;
  height: 320px;
}

.course-card:hover {
  transform: translateY(-4px);
  box-shadow: 0 8px 25px rgba(0, 0, 0, 0.1);
  border-color: #e0e0e0;
}

/* 课程封面 */
.course-cover {
  width: 100%;
  height: 160px;
  overflow: hidden;
  position: relative;
}

.cover-image {
  width: 100%;
  height: 100%;
  object-fit: cover;
  transition: transform 0.3s ease;
}

.course-card:hover .cover-image {
  transform: scale(1.05);
}

/* 课程内容 */
.course-content {
  padding: 16px;
  flex: 1;
  display: flex;
  flex-direction: column;
  gap: 12px;
}

.course-title {
  font-size: 16px;
  font-weight: 600;
  color: #333;
  margin: 0;
  line-height: 1.4;
  overflow: hidden;
  text-overflow: ellipsis;
  display: -webkit-box;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
}

.course-description {
  font-size: 13px;
  color: #666;
  margin: 0;
  line-height: 1.5;
  overflow: hidden;
  text-overflow: ellipsis;
  display: -webkit-box;
  -webkit-line-clamp: 3;
  -webkit-box-orient: vertical;
  flex: 1;
}

/* 课程标签 */
.course-tags {
  display: flex;
  flex-wrap: wrap;
  gap: 6px;
  margin-top: auto;
}

.tag {
  display: inline-block;
  padding: 4px 8px;
  background: #f5f5f5;
  color: #666;
  font-size: 11px;
  border-radius: 12px;
  white-space: nowrap;
  transition: all 0.2s ease;
}

.tag:hover {
  background: #e8f4fd;
  color: #1976d2;
}

/* 响应式调整 */
@media (max-width: 768px) {
  .course-card {
    height: 300px;
  }

  .course-cover {
    height: 140px;
  }

  .course-content {
    padding: 14px;
  }

  .course-title {
    font-size: 15px;
  }

  .course-description {
    font-size: 12px;
  }
}
</style>
