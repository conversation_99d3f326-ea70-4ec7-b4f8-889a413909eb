<template>
  <header class="header">
    <div class="header-container">
      <div class="header-content">
        <!-- Logo -->
        <div class="logo-section">
          <router-link to="/" class="logo-link">
            <div class="logo-icon">
              <t-icon name="home" />
            </div>
            <span class="logo-text">家政帮手</span>
          </router-link>
        </div>

        <!-- Desktop Navigation -->
        <nav class="desktop-nav">
          <router-link
            to="/"
            class="nav-link"
            active-class="nav-link-active"
          >
            首页
          </router-link>
          <router-link
            to="/services"
            class="nav-link"
            active-class="nav-link-active"
          >
            全部服务
          </router-link>
          <router-link
            to="/my-bookings"
            class="nav-link"
            active-class="nav-link-active"
          >
            我的预约
          </router-link>
          <router-link
            to="/join-us"
            class="nav-link"
            active-class="nav-link-active"
          >
            加入我们
          </router-link>
        </nav>

        <!-- User Actions -->
        <div class="user-actions">
          <!-- Search -->
          <t-button variant="text" class="search-btn">
            <t-icon name="search" />
          </t-button>

          <!-- 已登录状态：显示用户头像和菜单 -->
          <t-dropdown v-if="isLoggedIn" placement="bottom-right">
            <div class="user-profile">
              <div class="user-avatar">
                <UserRoundCheck class="avatar-icon" />
              </div>
              <span class="user-name">{{ displayName }}</span>
              <t-icon name="chevron-down" class="dropdown-icon" />
            </div>
            <t-dropdown-menu>
              <t-dropdown-item @click="router.push('/my-bookings')">
                <t-icon name="calendar" style="margin-right: 8px;" />
                我的预约
              </t-dropdown-item>
              <t-dropdown-item @click="router.push('/profile')">
                <t-icon name="user" style="margin-right: 8px;" />
                个人中心
              </t-dropdown-item>
              <t-dropdown-item divider />
              <t-dropdown-item @click="handleLogout" theme="error">
                <t-icon name="logout" style="margin-right: 8px;" />
                退出登录
              </t-dropdown-item>
            </t-dropdown-menu>
          </t-dropdown>

          <!-- 未登录状态：显示登录按钮 -->
          <t-button v-else theme="primary" @click="handleLogin">登录</t-button>

          <!-- Mobile Menu Button -->
          <t-button
            variant="text"
            class="mobile-menu-btn"
            @click="toggleMobileMenu"
          >
            <t-icon :name="isMobileMenuOpen ? 'close' : 'menu'" />
          </t-button>
        </div>
      </div>

      <!-- Mobile Navigation -->
      <div v-show="isMobileMenuOpen" class="mobile-nav">
        <div class="mobile-nav-content">
          <router-link
            to="/"
            class="mobile-nav-link"
            @click="closeMobileMenu"
          >
            首页
          </router-link>
          <router-link
            to="/services"
            class="mobile-nav-link"
            @click="closeMobileMenu"
          >
            服务项目
          </router-link>
          <router-link
            to="/my-bookings"
            class="mobile-nav-link"
            @click="closeMobileMenu"
          >
            我的预约
          </router-link>
          <router-link
            to="/join-us"
            class="mobile-nav-link"
            @click="closeMobileMenu"
          >
            加入我们
          </router-link>
        </div>
      </div>
    </div>
  </header>
</template>

<script setup>
import { ref, computed } from 'vue'
import { useRouter } from 'vue-router'
import { GetWeChatParams } from '@/api/wxLogin.js'
import { MessagePlugin } from 'tdesign-vue-next';
import { useUserStore } from '@/store/modules/user.js'
import { UserRoundCheck } from 'lucide-vue-next'

const router = useRouter()
const userStore = useUserStore()

// 计算属性：是否已登录
const isLoggedIn = computed(() => !!userStore.token)

// 计算属性：用户显示名称
const displayName = computed(() => userStore.name || '用户')




// 移动端菜单状态
const isMobileMenuOpen = ref(false)

// 切换移动端菜单
const toggleMobileMenu = () => {
  isMobileMenuOpen.value = !isMobileMenuOpen.value
}

// 关闭移动端菜单
const closeMobileMenu = () => {
  isMobileMenuOpen.value = false
}

// 处理登录
const handleLogin = async () => {
  await router.push('/login')
}

// 处理登出
const handleLogout = async () => {
  try {
    await userStore.logout()
    MessagePlugin.success('已成功退出登录')
    // 如果当前页面需要登录权限，跳转到首页
    if (router.currentRoute.value.path !== '/') {
      router.push('/')
    }
  } catch (error) {
    MessagePlugin.error('退出登录失败，请稍后重试')
  }
}


</script>

<style scoped lang="less">
.header {
  background: #fff;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  position: sticky;
  top: 0;
  z-index: 1000;

  .header-container {
    max-width: 1200px;
    margin: 0 auto;
    padding: 0 20px;

    .header-content {
      display: flex;
      justify-content: space-between;
      align-items: center;
      height: 64px;

      .logo-section {
        .logo-link {
          display: flex;
          align-items: center;
          text-decoration: none;
          color: inherit;

          .logo-icon {
            width: 40px;
            height: 40px;
            background: linear-gradient(135deg, #1890ff, #096dd9);
            border-radius: 8px;
            display: flex;
            align-items: center;
            justify-content: center;
            margin-right: 8px;
            color: #fff;
            font-size: 20px;
          }

          .logo-text {
            font-size: 20px;
            font-weight: bold;
            color: #262626;
          }
        }
      }

      .desktop-nav {
        display: flex;
        gap: 32px;

        .nav-link {
          color: #595959;
          text-decoration: none;
          padding: 8px 12px;
          border-radius: 6px;
          font-size: 14px;
          font-weight: 500;
          transition: all 0.3s ease;

          &:hover {
            color: #1890ff;
            background: #e6f7ff;
          }

          &.nav-link-active {
            color: #1890ff;
            background: #e6f7ff;
          }
        }

        @media (max-width: 768px) {
          display: none;
        }
      }

      .user-actions {
        display: flex;
        align-items: center;
        gap: 8px;

        .search-btn {
          @media (max-width: 640px) {
            display: none;
          }
        }

        .user-profile {
          display: flex;
          align-items: center;
          gap: 8px;
          padding: 8px 12px;
          border-radius: 8px;
          cursor: pointer;
          transition: all 0.3s ease;
          background: #f8f9fa;

          &:hover {
            background: #e9ecef;
          }

          .user-avatar {
            width: 32px;
            height: 32px;
            display: flex;
            align-items: center;
            justify-content: center;

            .avatar-icon {
              width: 24px;
              height: 24px;
            }
          }

          .user-name {
            font-size: 14px;
            font-weight: 500;
            color: #262626;
            max-width: 80px;
            overflow: hidden;
            text-overflow: ellipsis;
            white-space: nowrap;

            @media (max-width: 640px) {
              display: none;
            }
          }

          .dropdown-icon {
            font-size: 12px;
            color: #8c8c8c;
            transition: transform 0.3s ease;
          }
        }

        .mobile-menu-btn {
          @media (min-width: 769px) {
            display: none;
          }
        }
      }
    }

    .mobile-nav {
      border-top: 1px solid #f0f0f0;

      .mobile-nav-content {
        padding: 8px 0;

        .mobile-nav-link {
          display: block;
          padding: 12px 16px;
          color: #595959;
          text-decoration: none;
          font-size: 16px;
          font-weight: 500;
          border-radius: 6px;
          transition: all 0.3s ease;

          &:hover {
            color: #1890ff;
            background: #e6f7ff;
          }
        }
      }

      @media (min-width: 769px) {
        display: none;
      }
    }
  }
}
</style>
