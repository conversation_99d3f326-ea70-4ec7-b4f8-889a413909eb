<template>
  <div class="course-grid-container">
    <div class="course-grid">
      <CourseCard
        v-for="course in courseList"
        :key="course.id"
        :course="course"
      />
    </div>

    <!-- 分页组件 -->
    <div class="pagination-container">
      <t-pagination
        v-model="currentPage"
        :total="totalCourses"
        :page-size="pageSize"
        :show-sizer="true"
        :show-jumper="true"
        :page-size-options="pageSizeOptions"
        @change="onChange"
      />
    </div>
  </div>
</template>

<script setup>
import { ref, computed, onMounted, onUnmounted } from 'vue'
import CourseCard from './CourseCard.vue'
import { ListCourse } from '@/api/course.js'

// 分页相关的响应式数据
const currentPage = ref(1)
const pageSize = ref(8) // 默认每页显示8个课程
const totalCourses = ref(0)
const courseList = ref([])

// 每页条数选项 - TDesign 分页组件格式
const pageSizeOptions = ref([5, 8, 10, 15, 20])

// 获取课程列表数据
const listCourse = async (queryParam) => {
  const response = await ListCourse(queryParam)
  courseList.value = response.rows
  totalCourses.value = response.total
}

// 查询对象
const queryParams = ref({
  pageNum: 1,
  pageSize: 8,
  courseName: undefined,
  introduce: undefined,
  teacher: undefined,
  teacherPhoto: undefined,
  tag: undefined,
});


// 分页事件处理函数
const onChange = async (pageInfo) => {
  console.log('page-info:', pageInfo)
  currentPage.value = pageInfo.current
  pageSize.value = pageInfo.pageSize

  // 更新查询参数
  queryParams.value.pageNum = pageInfo.current
  queryParams.value.pageSize = pageInfo.pageSize

  // 重新获取数据
  await listCourse(queryParams.value)

  // 滚动到顶部
  window.scrollTo({
    top: 0,
    behavior: 'smooth'
  })
}

// 响应式调整每页显示数量
const updatePageSize = () => {
  const width = window.innerWidth
  if (width < 768) {
    pageSize.value = 4 // 移动端每页显示4个
  } else if (width < 1024) {
    pageSize.value = 6 // 平板每页显示6个
  } else if (width < 1200) {
    pageSize.value = 9 // 中等屏幕每页显示9个
  } else {
    pageSize.value = 8 // 大屏幕每页显示8个
  }
}


listCourse(queryParams.value)
</script>

<style scoped>
.course-grid-container {
  padding: 0 0 30px;
  width: 100%;
}

.course-grid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(280px, 1fr));
  gap: 16px;
  align-items: stretch;
  justify-content: center;
  width: 100%;
}

.pagination-container {
  display: flex;
  justify-content: center;
  align-items: center;
  padding: 30px 0 10px;
  width: 100%;
}

@media (max-width: 768px) {
  .course-grid-container {
    padding: 0 0 30px;
  }

  .course-grid {
    grid-template-columns: 1fr;
    gap: 12px;
  }

  .pagination-container {
    padding: 25px 0 10px;
  }

  .pagination-container :deep(.t-pagination) {
    font-size: 14px;
  }
}

@media (min-width: 768px) and (max-width: 1024px) {
  .course-grid {
    grid-template-columns: repeat(2, 1fr);
    gap: 14px;
  }
}

@media (min-width: 1024px) and (max-width: 1200px) {
  .course-grid {
    grid-template-columns: repeat(3, 1fr);
    gap: 16px;
  }
}

@media (min-width: 1200px) {
  .course-grid {
    grid-template-columns: repeat(4, 1fr);
    gap: 16px;
  }
}
</style>
