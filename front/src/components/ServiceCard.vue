<template>
  <div class="service-card" @click="goToServiceDetail">
    <!-- 服务封面 -->
    <div class="service-cover">
      <img :src="service.serviceImage" :alt="service.serviceName" class="cover-image" />
      <div class="category-badge-cover">{{ service.category }}</div>
    </div>

    <!-- 服务内容 -->
    <div class="service-content">
      <h3 class="service-title">{{ service.serviceName }}</h3>
      <p class="service-description">{{ service.description }}</p>
      <!-- 服务标签 -->
      <div class="service-tags">
        <span
          v-for="tag in parsedTags"
          :key="tag"
          class="tag"
        >
          {{ tag }}
        </span>
      </div>

      <!-- 价格显示 -->
      <div class="service-price">
        <span class="price-symbol">¥</span>
        <span class="price-number">{{ getPriceNumber(service.price) }}</span>
        <span class="price-unit">{{ getPriceUnit(service.price) }}</span>
      </div>
    </div>
  </div>
</template>

<script setup>
import { useRouter } from 'vue-router'
import { computed } from 'vue'

const router = useRouter()

const props = defineProps({
  service: {
    type: Object,
    required: true
  }
})

// 处理标签数据，支持字符串和数组格式
const parsedTags = computed(() => {
  if (!props.service.tags) return []

  // 如果是字符串，按逗号分割
  if (typeof props.service.tags === 'string') {
    return props.service.tags.split(',').map(tag => tag.trim()).filter(tag => tag)
  }

  // 如果是数组，直接返回
  if (Array.isArray(props.service.tags)) {
    return props.service.tags
  }

  return []
})

// 获取价格数字部分
const getPriceNumber = (price) => {
  const match = price.match(/(\d+)/)
  return match ? match[1] : ''
}

// 获取价格单位部分
const getPriceUnit = (price) => {
  const match = price.match(/\d+(.*)/)
  return match ? match[1] : ''
}

// 跳转到服务详情页面
const goToServiceDetail = () => {
  router.push(`/service/${props.service.serviceId}`)
}
</script>

<style scoped>
.service-card {
  background: #fff;
  border-radius: 12px;
  overflow: hidden;
  transition: all 0.3s ease;
  cursor: pointer;
  border: 1px solid #f0f0f0;
  display: flex;
  flex-direction: column;
  height: 320px;
  color: inherit;
}

.service-card:hover {
  transform: translateY(-4px);
  box-shadow: 0 8px 25px rgba(0, 0, 0, 0.1);
  border-color: #e0e0e0;
}

/* 服务封面 */
.service-cover {
  width: 100%;
  height: 160px;
  overflow: hidden;
  position: relative;
}

.cover-image {
  width: 100%;
  height: 100%;
  object-fit: cover;
  transition: transform 0.3s ease;
}

.service-card:hover .cover-image {
  transform: scale(1.05);
}

.category-badge-cover {
  position: absolute;
  top: 12px;
  right: 12px;
  background: rgba(74, 144, 226, 0.9);
  color: white;
  padding: 4px 8px;
  border-radius: 12px;
  font-size: 12px;
  font-weight: 600;
  backdrop-filter: blur(4px);
}

/* 服务内容 */
.service-content {
  padding: 16px 16px 12px 16px;
  flex: 1;
  display: flex;
  flex-direction: column;
  gap: 0;
}

.service-title {
  font-size: 16px;
  font-weight: 600;
  color: #333;
  margin: 0 0 8px 0;
  line-height: 1.4;
  overflow: hidden;
  text-overflow: ellipsis;
  display: -webkit-box;
  -webkit-line-clamp: 1;
  -webkit-box-orient: vertical;
}

.service-description {
  font-size: 13px;
  color: #666666;
  margin: 0 0 8px 0;
  line-height: 1.5;
  overflow: hidden;
  text-overflow: ellipsis;
  display: -webkit-box;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
  flex: 1;
  word-wrap: break-word;
  font-weight: normal;
  /* 精确控制高度，确保只显示两行 */
  max-height: 3em; /* 2行 * 1.5行高 = 3em */
  height: 3em; /* 固定高度 */
  /* 重置所有可能影响颜色的属性 */
  background: transparent;
  text-shadow: none;
  -webkit-text-stroke: 0;
}

/* 服务价格 */
.service-price {
  margin-top: 8px;
  margin-bottom: 0;
  display: flex;
  align-items: baseline;
}

.price-symbol {
  color: #ff4f24;
  font-size: 14px;
  font-weight: 600;
  margin-right: 2px;
}

.price-number {
  color: #ff4f24;
  font-size: 20px;
  font-weight: 700;
  font-family: Arial, sans-serif;
}

.price-unit {
  color: #ff4f24;
  font-size: 12px;
  font-weight: 500;
  margin-left: 2px;
}

/* 服务标签 */
.service-tags {
  display: flex;
  flex-wrap: wrap;
  gap: 6px;
  margin: 0;
}

.tag {
  display: inline-block;
  padding: 4px 8px;
  background: #e3f2fd;
  color: #1976d2;
  font-size: 11px;
  border-radius: 12px;
  white-space: nowrap;
  transition: all 0.2s ease;
  border: 1px solid #bbdefb;
}

.tag:hover {
  background: #bbdefb;
  color: #0d47a1;
}

/* 响应式调整 */
@media (max-width: 768px) {
  .service-card {
    height: 340px;
  }

  .service-cover {
    height: 140px;
  }

  .service-content {
    padding: 14px;
  }

  .service-title {
    font-size: 15px;
  }

  .service-description {
    font-size: 12px;
  }
}
</style>
