# ImageUpload 图片上传组件

一个基于 TDesign Upload 组件封装的图片上传组件，参考ruoyi项目实现，提供了完整的图片上传功能。

## 功能特性

- 支持拖拽上传和点击上传
- 文件类型和大小验证
- 图片压缩功能
- 支持单张和多张图片上传
- 支持URL和ID两种模式
- 文件回显功能
- 完整的事件回调
- 自动删除OSS文件

## 基本用法

```vue
<template>
  <ImageUpload
    v-model="imageUrls"
    :limit="3"
    :file-size="10"
    @change="handleChange"
  />
</template>

<script setup>
import { ref } from 'vue'
import ImageUpload from '@/components/ImageUpload/index.vue'

const imageUrls = ref('')

const handleChange = (value) => {
  console.log('图片变化:', value)
}
</script>
```

## Props

| 参数 | 类型 | 默认值 | 说明 |
|------|------|--------|------|
| modelValue | String/Array | '' | v-model 绑定的值，URL模式为字符串，ID模式为字符串 |
| abridgeName | Array | [] | 文件名过长时省略中间文本，保留首尾文本长度 |
| limit | Number | 5 | 图片数量限制，为0不限制 |
| draggable | Boolean | false | 是否支持拖拽上传 |
| fileSize | Number | 5 | 大小限制(MB) |
| accept | Array | ['image/gif', 'image/jpeg', ...] | 接受上传的文件类型 |
| fileType | Array | ['png', 'jpg', 'jpeg', 'gif', 'webp', 'bmp'] | 文件类型后缀 |
| isShowTip | Boolean | true | 是否显示提示 |
| theme | String | 'image' | 上传组件主题 |
| mode | String | 'url' | 模式，id模式返回ossId，url模式返回url链接 |
| disabled | Boolean | false | 禁用组件 |
| allowUploadDuplicateFile | Boolean | false | 是否允许重复上传相同文件名的文件 |
| ossCategoryId | Number | undefined | oss分类id |
| compressSupport | Boolean | false | 是否支持压缩 |
| compressTargetSize | Number | 300 | 压缩目标大小，单位KB |

## Events

| 事件名 | 参数 | 说明 |
|--------|------|------|
| update:modelValue | value | v-model 更新事件，返回URL字符串或ID字符串 |
| change | value | 值变化事件，返回URL字符串或ID字符串 |

## 高级用法

### 多图片上传

```vue
<template>
  <ImageUpload
    v-model="imageUrls"
    :limit="5"
    :file-size="10"
    :file-type="['jpg', 'png', 'gif']"
    @change="handleChange"
  />
</template>

<script setup>
import { ref } from 'vue'

const imageUrls = ref('')

const handleChange = (value) => {
  console.log('图片URLs:', value)
  // value 格式: "http://example.com/1.jpg,http://example.com/2.jpg"
}
</script>
```

### 启用图片压缩

```vue
<template>
  <ImageUpload
    v-model="imageUrls"
    :compress-support="true"
    :compress-target-size="500"
    :file-size="20"
    @change="handleChange"
  />
</template>

<script setup>
// 启用压缩后，大于500KB的图片会自动压缩到500KB以内
</script>
```

### ID模式使用

```vue
<template>
  <ImageUpload
    v-model="ossIds"
    mode="id"
    :limit="3"
    @change="handleChange"
  />
</template>

<script setup>
import { ref } from 'vue'

const ossIds = ref('')

const handleChange = (value) => {
  console.log('OSS IDs:', value)
  // value 格式: "123,456,789"
}
</script>
```

## API接口要求

组件需要以下API接口支持：

### 上传接口
- **URL**: `/resource/oss/upload`
- **方法**: POST
- **参数**: FormData格式，包含文件和ossCategoryId
- **返回格式**:
```json
{
  "code": 200,
  "data": {
    "fileName": "image.jpg",
    "url": "http://example.com/image.jpg",
    "ossId": 123
  }
}
```

### 查询接口
- **根据URLs查询**: `/resource/oss/listByUrls?urls=encodeURIComponent(urls)`
- **根据IDs查询**: `/resource/oss/listByIds/{ids}`
- **删除文件**: `/resource/oss/{ossId}` (DELETE方法)

## 注意事项

1. 组件依赖 TDesign Vue Next 和 image-conversion，请确保已正确安装
2. 需要配置正确的后端API接口
3. 组件会自动处理 token 认证，从用户store中获取token
4. 支持图片压缩功能，可以有效减少上传文件大小
5. 删除图片时会自动调用删除API清理OSS文件
6. URL模式和ID模式的返回值格式不同，请根据需要选择
