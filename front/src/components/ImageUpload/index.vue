<template>
  <div class="component-upload-image">
    <t-upload
      ref="imageUpload"
      v-model="fileList"
      multiple
      :abridge-name="abridgeName"
      :theme="theme"
      :accept="rawAccept"
      :action="uploadImgUrl"
      :before-upload="handleBeforeUpload"
      :max="limit"
      :on-fail="handleUploadError"
      :headers="headers"
      :draggable="draggable"
      :size-limit="{ size: fileSize, unit: 'MB', message: '上传图片大小不能超过 {sizeLimit} MB!' }"
      :disabled="disabled"
      :allow-upload-duplicate-file="allowUploadDuplicateFile"
      :data="{ ossCategoryId: ossCategoryId || '' }"
      @one-file-success="handleOneUploadSuccess"
      @success="handleUploadSuccess"
      @remove="handleDelete"
      @validate="onValidate"
    >
      <template v-if="isShowTip" #tips>
        请上传大小不超过 {{ fileSize }}MB 的图片，
        <t-tooltip>
          <template #content>
            <p v-for="item in rawAccept?.split(',')" :key="item" style="word-break: break-all">
              {{ item }}
            </p>
          </template>
          <t-link theme="primary">查看格式要求</t-link>
        </t-tooltip>
      </template>
      <!-- 上传按钮 -->
      <slot />
      <t-button v-if="!$slots.default" variant="outline">
        <template #icon>
          <cloud-upload-icon />
        </template>
        选取文件
      </t-button>
    </t-upload>
  </div>
</template>

<script setup>
import { compressAccurately } from 'image-conversion';
import { CloudUploadIcon } from 'tdesign-icons-vue-next';
import { MessagePlugin } from 'tdesign-vue-next';
import { computed, ref, watch } from 'vue';

import { delOss, listByIds, listByUrls } from '@/api/oss';
import { useUserStore } from '@/store/modules/user';
import { blobToFile, getHttpFileName, getHttpFileSuffix } from '@/utils/file';

// Props 定义
const props = defineProps({
  modelValue: {
    type: [String, Array],
    default: ''
  },
  // 文件名过长时，需要省略中间的文本，保留首尾文本。示例：[10, 7]，表示首尾分别保留的文本长度。
  abridgeName: {
    type: Array,
    default: () => []
  },
  // 图片数量限制,为0不限制
  limit: {
    type: Number,
    default: 5
  },
  // 是否支持拖拽上传
  draggable: {
    type: Boolean,
    default: false
  },
  // 大小限制(MB)
  fileSize: {
    type: Number,
    default: 5
  },
  // 接受上传的文件类型
  accept: {
    type: Array,
    default: () => ['image/gif', 'image/jpeg', 'image/png', 'image/svg+xml', 'image/tiff', 'image/vnd.wap.wbmp', 'image/webp', 'image/x-icon', 'image/x-jng', 'image/x-ms-bmp']
  },
  // 文件类型, 例如['png', 'jpg', 'jpeg']
  fileType: {
    type: Array,
    default: () => ['png', 'jpg', 'jpeg', 'gif', 'webp', 'bmp']
  },
  // 是否显示提示
  isShowTip: {
    type: Boolean,
    default: true
  },
  theme: {
    type: String,
    default: 'image'
  },
  // 模式，id模式返回ossId，url模式返回url链接
  mode: {
    type: String,
    default: 'url'
  },
  // 禁用组件
  disabled: {
    type: Boolean,
    default: false
  },
  // 是否允许重复上传相同文件名的文件
  allowUploadDuplicateFile: {
    type: Boolean,
    default: false
  },
  // oss分类id
  ossCategoryId: {
    type: Number,
    default: undefined
  },
  // 是否支持压缩，默认否
  compressSupport: {
    type: Boolean,
    default: false
  },
  // 压缩目标大小，单位KB。默认300KB以上文件才压缩，并压缩至300KB以内
  compressTargetSize: {
    type: Number,
    default: 300
  }
})

const userStore = useUserStore();
const emit = defineEmits(['update:modelValue', 'change']);
const baseUrl = import.meta.env.VITE_APP_BASE_API;
const uploadImgUrl = 'http://localhost:8080/resource/oss/upload'; // 上传的图片服务器地址
const headers = ref({ Authorization: `Bearer ${userStore.token || ''}` });
const fileList = ref([]);
const rawAccept = computed(() => {
  return props.accept?.join(',') || props.fileType.map((value) => `.${value}`).join(',');
});

watch(
  () => props.modelValue,
  async (val) => {
    if (val) {
      // 首先将值转为数组
      let list = [];
      if (Array.isArray(val)) {
        list = val;
      } else if (props.mode === 'url') {
        try {
          const res = await listByUrls(val);
          const tempMap = new Map();
          res.data.forEach((oss) => {
            tempMap.set(oss.url, {
              name: oss.originalName,
              status: 'success',
              size: oss.size,
              uploadTime: oss.createTime,
              url: oss.url,
              ossId: oss.ossId,
            });
          });
          list = val.split(/,(?=http)/).map((url) => {
            return (
              tempMap.get(url) || {
                name: getHttpFileName(url),
                status: 'success',
                size: 0,
                url,
              }
            );
          });
        } catch (error) {
          console.error('获取图片信息失败:', error);
          list = val.split(/,(?=http)/).map((url) => ({
            name: getHttpFileName(url),
            status: 'success',
            size: 0,
            url,
          }));
        }
      } else if (props.mode === 'id') {
        try {
          const res = await listByIds(val);
          list = res.data.map((oss) => {
            return {
              name: oss.originalName,
              status: 'success',
              size: oss.size,
              uploadTime: oss.createTime,
              url: oss.url,
              ossId: oss.ossId,
            };
          });
        } catch (error) {
          console.error('获取图片信息失败:', error);
          list = [];
        }
      }
      // 然后将数组转为对象数组
      fileList.value = list.map((item) => {
        // 字符串回显处理 如果此处存的是url可直接回显 如果存的是id需要调用接口查出来
        if (typeof item === 'string') {
          item = { name: getHttpFileName(item), status: 'success', url: item };
        } else {
          // 此处name使用ossId 防止删除出现重名
          item = {
            name: item.name,
            status: item.status,
            size: item.size,
            uploadTime: item.uploadTime,
            url: item.url,
            ossId: item.ossId,
          };
        }
        return item;
      });
    } else {
      fileList.value = [];
    }
  },
  { deep: true, immediate: true },
);

// 有文件数量超出时会触发，文件大小超出限制、文件同名时会触发等场景。注意如果设置允许上传同名文件，则此事件不会触发
const onValidate = (params) => {
  const { files, type } = params;
  if (type === 'FILE_OVER_SIZE_LIMIT') {
    files.map((t) => t.name).join('、');
    MessagePlugin.warning(`${files.map((t) => t.name).join('、')} 等文件大小超出限制，已自动过滤`, 5000);
  } else if (type === 'FILES_OVER_LENGTH_LIMIT') {
    MessagePlugin.warning('文件数量超出限制，仅上传未超出数量的文件');
  } else if (type === 'FILTER_FILE_SAME_NAME') {
    // 如果希望支持上传同名文件，请设置 allowUploadDuplicateFile={true}
    MessagePlugin.warning('不允许上传同名文件');
  }
};

// 上传前loading加载
async function handleBeforeUpload(file) {
  let isImg;
  if (props.fileType.length) {
    let fileExtension = '';
    if (file.name.lastIndexOf('.') > -1) {
      fileExtension = getHttpFileSuffix(file.name);
    }
    isImg = props.fileType.some((type) => {
      if (file.type.indexOf(type) > -1) return true;
      return fileExtension && fileExtension.indexOf(type) > -1;
    });
  } else {
    isImg = file.type.indexOf('image') > -1;
  }
  if (!isImg) {
    MessagePlugin.error(`文件格式不正确, 请上传${props.fileType.join('/')}图片格式文件!`);
    return false;
  }
  if (file.name.includes(',')) {
    MessagePlugin.error('文件名不正确，不能包含英文逗号!');
    return false;
  }
  // 压缩图片，开启压缩并且大于指定的压缩大小时才压缩
  if (props.compressSupport && file.size / 1024 > props.compressTargetSize) {
    const blob = await compressAccurately(file.raw, props.compressTargetSize);
    file.raw = blobToFile(blob, file.name, file.type);
    file.size = file.raw.size;
  }
  return true;
}

// 单上传成功回调，多选回调多次
function handleOneUploadSuccess(context) {
  if (context.response.code !== 200) {
    MessagePlugin.error(context.response.msg);
  } else {
    MessagePlugin.success(`文件【${context.response.data.fileName}】上传成功！`);
  }
}

// 上传成功回调
function handleUploadSuccess(context) {
  const uploadList = context.results
    .filter((value) => value.response.code === 200)
    .map((value) => {
      return { name: value.response.data.fileName, url: value.response.data.url, ossId: value.response.data.ossId };
    });
  uploadedSuccessfully(uploadList);
}

// 删除图片
function handleDelete({ file }) {
  const { ossId, name } = file;
  // 直接上传时，删除旧数据
  if (ossId) {
    delOss(ossId).then(() => {
      MessagePlugin.success(`文件【${name}】删除成功！`);
    }).catch(() => {
      MessagePlugin.error(`文件【${name}】删除失败！`);
    });
  }
  const value = listToString(fileList.value);
  emit('update:modelValue', value);
  emit('change', value);
}

// 上传结束处理
function uploadedSuccessfully(uploadList) {
  fileList.value = fileList.value.filter((f) => f.url !== undefined).concat(uploadList);
  const value = listToString(fileList.value);
  emit('update:modelValue', value);
  emit('change', value);
}

// 上传失败
function handleUploadError() {
  MessagePlugin.error('上传图片失败');
}

// 对象转成指定字符串分隔
function listToString(list, separator) {
  let strs = '';
  separator = separator || ',';
  for (const i in list) {
    if (props.mode === 'url') {
      if (list[i].url && list[i].url.indexOf('blob:') !== 0) {
        strs += list[i].url + separator;
      }
    } else if (props.mode === 'id') {
      if (list[i].ossId && list[i].url.indexOf('blob:') !== 0) {
        strs += list[i].ossId + separator;
      }
    }
  }
  return strs !== '' ? strs.substring(0, strs.length - 1) : '';
}
</script>

<style lang="less" scoped>
.component-upload-image {
  width: 100%;
}
</style>
