<template>
  <div class="service-grid-container">
    <div class="service-grid">
      <ServiceCard
        v-for="service in serviceList"
        :key="service.id"
        :service="service"
      />
    </div>

    <!-- 分页组件 -->
    <div class="pagination-container">
      <t-pagination
        v-model="currentPage"
        :total="totalServices"
        :page-size="pageSize"
        :show-sizer="true"
        :show-jumper="true"
        :page-size-options="pageSizeOptions"
        @change="onChange"
      />
    </div>
  </div>
</template>

<script setup>
import { ref, onMounted } from 'vue'
import ServiceCard from './ServiceCard.vue'
import { ListServe } from '@/api/serve.js'


const listServe = async () => {
  const response = await ListServe({
    pageNum: currentPage.value,
    pageSize: pageSize.value,
  })
  console.log("获取到的数据",response);
}

// 分页相关的响应式数据
const currentPage = ref(1)
const pageSize = ref(8) // 默认每页显示8个服务
const totalServices = ref(0)
const serviceList = ref([])

// 每页条数选项 - TDesign 分页组件格式
const pageSizeOptions = ref([5, 8, 10, 15, 20])

// Mock数据
const mockServices = [
  {
    id: 1,
    serviceName: '家庭保洁',
    description: '专业清洁团队，深度清洁您的家居环境，让您的家焕然一新。包含客厅、卧室、厨房、卫生间全面清洁。',
    serviceImage: 'https://images.unsplash.com/photo-1581578731548-c64695cc6952?w=400&h=300&fit=crop',
    price: '¥80起/次',
    category: '清洁服务',
    tags: ['日常保洁', '深度清洁', '开荒保洁'],
    serviceId: 1
  },
  {
    id: 2,
    serviceName: '月嫂服务',
    description: '专业月嫂，为新生儿和产妇提供专业护理，科学坐月子，让妈妈和宝宝都得到最好的照顾。',
    serviceImage: 'https://images.unsplash.com/photo-1544367567-0f2fcb009e0b?w=400&h=300&fit=crop',
    price: '¥8800起/月',
    category: '母婴护理',
    tags: ['新生儿护理', '产妇护理', '营养配餐'],
    serviceId: 2
  },
  {
    id: 3,
    serviceName: '育儿嫂',
    description: '专业育儿师，科学育儿，陪伴宝宝健康成长，提供专业的婴幼儿护理和早教服务。',
    serviceImage: 'https://images.unsplash.com/photo-1476703993599-0035a21b17a9?w=400&h=300&fit=crop',
    price: '¥6800起/月',
    category: '母婴护理',
    tags: ['婴幼儿护理', '早教启蒙', '辅食制作'],
    serviceId: 3
  },
  {
    id: 4,
    serviceName: '老人护理',
    description: '贴心护理，让老人享受温馨晚年，专业照护服务，24小时贴心陪伴。',
    serviceImage: 'https://images.unsplash.com/photo-1559839734-2b71ea197ec2?w=400&h=300&fit=crop',
    price: '¥5800起/月',
    category: '老人护理',
    tags: ['生活照料', '健康监护', '陪伴聊天'],
    serviceId: 4
  },
  {
    id: 5,
    serviceName: '钟点工',
    description: '灵活的钟点服务，按需预约，专业可靠，为您分担家务负担。',
    serviceImage: 'https://images.unsplash.com/photo-1527515637462-cff94eecc1ac?w=400&h=300&fit=crop',
    price: '¥35起/小时',
    category: '清洁服务',
    tags: ['按时计费', '灵活预约', '家务协助'],
    serviceId: 5
  },
  {
    id: 6,
    serviceName: '搬家服务',
    description: '专业搬家团队，安全快捷，提供打包、搬运、安装一站式服务。',
    serviceImage: 'https://images.unsplash.com/photo-1558618666-fcd25c85cd64?w=400&h=300&fit=crop',
    price: '¥300起/次',
    category: '搬家服务',
    tags: ['专业搬运', '打包服务', '家具安装'],
    serviceId: 6
  },
  {
    id: 7,
    serviceName: '深度保洁',
    description: '全屋深度清洁，包含家电清洗，让家居环境焕然一新，除菌除螨更健康。',
    serviceImage: 'https://images.unsplash.com/photo-1563453392212-326f5e854473?w=400&h=300&fit=crop',
    price: '¥150起/次',
    category: '清洁服务',
    tags: ['全屋深度清洁', '家电清洗', '除菌除螨'],
    serviceId: 7
  },
  {
    id: 8,
    serviceName: '家电维修',
    description: '专业家电维修服务，快速上门，质量保证，让您的家电重新焕发活力。',
    serviceImage: 'https://images.unsplash.com/photo-1621905251189-08b45d6a269e?w=400&h=300&fit=crop',
    price: '¥80起/次',
    category: '维修服务',
    tags: ['快速上门', '质量保证', '多种家电'],
    serviceId: 8
  }
]

// 获取服务列表数据（使用mock数据）
const getServiceList = (queryParam) => {
  const { pageNum, pageSize: size } = queryParam
  const start = (pageNum - 1) * size
  const end = start + size

  serviceList.value = mockServices.slice(start, end)
  totalServices.value = mockServices.length
}

// 查询对象
const queryParams = ref({
  pageNum: 1,
  pageSize: 8,
  serviceName: undefined,
  category: undefined,
  tags: undefined,
})

// 分页事件处理函数
const onChange = (pageInfo) => {
  console.log('page-info:', pageInfo)
  currentPage.value = pageInfo.current
  pageSize.value = pageInfo.pageSize

  // 更新查询参数
  queryParams.value.pageNum = pageInfo.current
  queryParams.value.pageSize = pageInfo.pageSize

  // 重新获取数据
  getServiceList(queryParams.value)

  // 滚动到顶部
  window.scrollTo({
    top: 0,
    behavior: 'smooth'
  })
}

// 初始化数据
onMounted(() => {
  getServiceList(queryParams.value)
  listServe()
})
</script>

<style scoped>
.service-grid-container {
  padding: 0 0 30px;
  width: 100%;
}

.service-grid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(280px, 1fr));
  gap: 16px;
  align-items: stretch;
  justify-content: center;
  width: 100%;
}

.pagination-container {
  display: flex;
  justify-content: center;
  align-items: center;
  padding: 30px 0 10px;
  width: 100%;
}

@media (max-width: 768px) {
  .service-grid-container {
    padding: 0 0 30px;
  }

  .service-grid {
    grid-template-columns: 1fr;
    gap: 12px;
  }

  .pagination-container {
    padding: 25px 0 10px;
  }

  .pagination-container :deep(.t-pagination) {
    font-size: 14px;
  }
}

@media (min-width: 768px) and (max-width: 1024px) {
  .service-grid {
    grid-template-columns: repeat(2, 1fr);
    gap: 14px;
  }
}

@media (min-width: 1024px) and (max-width: 1200px) {
  .service-grid {
    grid-template-columns: repeat(3, 1fr);
    gap: 16px;
  }
}

@media (min-width: 1200px) {
  .service-grid {
    grid-template-columns: repeat(4, 1fr);
    gap: 16px;
  }
}
</style>
